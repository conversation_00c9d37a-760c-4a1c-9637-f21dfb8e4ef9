# OpenSIPS CE - Git Ignore File

# Docker volumes and runtime data
logs/
mysql_data/
opensips_logs/
opensips_fifo/
freeswitch_conf/
freeswitch_sounds/
freeswitch_logs/
redis_data/

# Environment files with sensitive data
.env
.env.local
.env.production
.env.staging

# SSL certificates and keys
ssl/*.key
ssl/*.pem
ssl/*.crt
ssl/*.p12

# Database backups
*.sql.backup
*.sql.gz
backup/
backups/

# Log files
*.log
*.log.*
logs/*.log

# Temporary files
*.tmp
*.temp
.tmp/
temp/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Node.js dependencies and build files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
dist/
build/

# Docker Compose override files
docker-compose.override.yml
docker-compose.local.yml

# Runtime configuration files that may contain sensitive data
opensips-cli.cfg.local
cp-config/local.inc.php

# FreeSWITCH runtime files
freeswitch/storage/
freeswitch/recordings/
freeswitch/voicemail/

# System files
.pid
*.pid
*.sock

# Backup configurations
*.cfg.backup
*.cfg.bak
opensips.cfg.backup

# Test files and coverage reports
coverage/
.nyc_output/
test-results/

# Package files
*.tar.gz
*.zip
*.rar

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Keep important example files
!.env.example
!ssl/README.md
!logs/README.md
