FROM opensips/opensips-cp:latest

# Create init script to configure database connection using environment variables
RUN cat > /docker-entrypoint-init.d/01-configure-db.sh << 'EOF'
#!/bin/bash

# Wait for environment variables to be available
if [ -n "$DB_HOST" ] && [ -n "$DB_USER" ] && [ -n "$DB_PASS" ] && [ -n "$DB_NAME" ]; then
    echo "Configuring OpenSIPS Control Panel database connection..."
    
    # Create db.inc.php with environment variables
    cat > /var/www/html/opensips-cp/config/db.inc.php << DBEOF
<?php
/*
 * OpenSIPS Control Panel Database Configuration
 * Auto-generated from environment variables
 */

global \$config;
if (!isset(\$config)) \$config = new stdClass();

// Database driver
\$config->db_driver = "mysql";

// Database connection from environment variables
\$config->db_host = "${DB_HOST}";
\$config->db_port = "";
\$config->db_user = "${DB_USER}";
\$config->db_pass = "${DB_PASS}";
\$config->db_name = "${DB_NAME}";

if (!empty(\$config->db_port)) \$config->db_host = \$config->db_host . ";port=" . \$config->db_port;

?>
DBEOF

    echo "Database configuration completed."
    
    # Set proper permissions
    chown www-data:www-data /var/www/html/opensips-cp/config/db.inc.php
    chmod 644 /var/www/html/opensips-cp/config/db.inc.php
else
    echo "Warning: Database environment variables not set!"
fi
EOF

# Make the script executable
RUN chmod +x /docker-entrypoint-init.d/01-configure-db.sh

# Ensure the script runs before Apache starts
RUN mkdir -p /docker-entrypoint-init.d
