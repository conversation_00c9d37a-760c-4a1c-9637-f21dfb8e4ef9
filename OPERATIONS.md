# OpenSIPS CE - Hướng dẫn vận hành

## Khởi động và dừng hệ thống

### Khởi động tất cả services
```bash
docker-compose up -d
```

### Khởi động service cụ thể
```bash
docker-compose up -d opensips
docker-compose up -d mysql
docker-compose up -d freeswitch
```

### Dừng hệ thống
```bash
# Dừng tất cả
docker-compose down

# Dừng và xóa volumes (cẩn thận!)
docker-compose down -v
```

### Restart services
```bash
# Restart tất cả
docker-compose restart

# Restart service cụ thể
docker-compose restart opensips
```

## Kiểm tra trạng thái

### Xem trạng thái services
```bash
docker-compose ps
```

### Kiểm tra logs
```bash
# Logs tất cả services
docker-compose logs

# Logs service cụ thể
docker-compose logs opensips
docker-compose logs mysql
docker-compose logs freeswitch

# Follow logs real-time
docker-compose logs -f opensips

# Logs với timestamp
docker-compose logs -t opensips

# Chỉ xem logs mới nhất
docker-compose logs --tail=50 opensips
```

### Ki<PERSON>m tra resource usage
```bash
# CPU và Memory usage
docker stats

# Disk usage
docker system df

# Network usage
docker network ls
```

## Truy cập services

### OpenSIPS
```bash
# SIP traffic
telnet localhost 5060

# Management interface (nếu có)
curl http://localhost:5080
```

### MySQL Database
```bash
# Kết nối database
mysql -h localhost -P 3307 -u opensips -p

# Backup database
mysqldump -h localhost -P 3307 -u opensips -p opensips > backup.sql

# Restore database
mysql -h localhost -P 3307 -u opensips -p opensips < backup.sql
```

### FreeSWITCH
```bash
# FreeSWITCH CLI
docker-compose exec freeswitch fs_cli

# Event Socket
telnet localhost 8021
```

### Control Panel
```bash
# Web interface
curl http://localhost:8081
```

## Troubleshooting

### OpenSIPS không khởi động
```bash
# Kiểm tra logs
docker-compose logs opensips

# Kiểm tra cấu hình
docker-compose exec opensips opensips -c

# Test cấu hình
docker-compose exec opensips opensips -C
```

### Database connection issues
```bash
# Test kết nối
docker-compose exec opensips mysql -h mysql -u opensips -p

# Kiểm tra MySQL logs
docker-compose logs mysql

# Reset MySQL password
docker-compose exec mysql mysql -u root -p -e "ALTER USER 'opensips'@'%' IDENTIFIED BY 'new_password';"
```

### FreeSWITCH issues
```bash
# Kiểm tra FreeSWITCH status
docker-compose exec freeswitch fs_cli -x "status"

# Reload configuration
docker-compose exec freeswitch fs_cli -x "reloadxml"

# Show calls
docker-compose exec freeswitch fs_cli -x "show calls"
```

### Network connectivity issues
```bash
# Test port connectivity
telnet localhost 5060
nc -zv localhost 5060

# Check listening ports
netstat -tlnp | grep 5060

# Test SIP with sipp
sipp -sn uac localhost:5060
```

## Monitoring và Maintenance

### Health Checks
```bash
# Script kiểm tra health
#!/bin/bash
echo "=== OpenSIPS Health Check ==="
docker-compose ps opensips
echo ""

echo "=== MySQL Health Check ==="
docker-compose exec mysql mysqladmin -u opensips -p ping
echo ""

echo "=== FreeSWITCH Health Check ==="
docker-compose exec freeswitch fs_cli -x "status" | head -5
echo ""

echo "=== Disk Usage ==="
df -h
echo ""

echo "=== Memory Usage ==="
free -h
```

### Log Rotation
```bash
# Cấu hình logrotate cho Docker logs
sudo tee /etc/logrotate.d/docker-opensips << EOF
/var/lib/docker/containers/*/*-json.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 0644 root root
    postrotate
        docker kill --signal=USR1 \$(docker ps -q) 2>/dev/null || true
    endscript
}
EOF
```

### Backup Procedures
```bash
#!/bin/bash
# Backup script
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/opensips_$DATE"

mkdir -p $BACKUP_DIR

# Backup database
docker-compose exec mysql mysqldump -u opensips -p opensips > $BACKUP_DIR/database.sql

# Backup configuration
cp opensips.cfg $BACKUP_DIR/
cp docker-compose.yml $BACKUP_DIR/

# Backup volumes
docker run --rm -v opensips_mysql_data:/data -v $BACKUP_DIR:/backup alpine tar czf /backup/mysql_data.tar.gz -C /data .

echo "Backup completed: $BACKUP_DIR"
```

## Performance Tuning

### OpenSIPS Tuning
```bash
# Trong opensips.cfg
udp_workers=8          # Tăng số workers
tcp_workers=4          # TCP workers
children=16            # Tổng số children processes

# Memory tuning
shm_mem=128            # Shared memory (MB)
pkg_mem=16             # Package memory (MB)
```

### MySQL Tuning
```bash
# Trong docker-compose.yml MySQL environment
MYSQL_INNODB_BUFFER_POOL_SIZE=1G
MYSQL_INNODB_LOG_FILE_SIZE=256M
MYSQL_MAX_CONNECTIONS=500
MYSQL_QUERY_CACHE_SIZE=128M
```

### System Tuning
```bash
# Tăng file descriptors
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# Network tuning
echo "net.core.rmem_max = 134217728" >> /etc/sysctl.conf
echo "net.core.wmem_max = 134217728" >> /etc/sysctl.conf
sysctl -p
```

## Security

### Firewall Configuration
```bash
# UFW rules
sudo ufw allow 5060/udp    # OpenSIPS SIP
sudo ufw allow 5060/tcp    # OpenSIPS SIP
sudo ufw allow 5090/udp    # FreeSWITCH
sudo ufw allow 8021/tcp    # FreeSWITCH ESL (chỉ từ localhost)
sudo ufw allow 8081/tcp    # Control Panel (chỉ từ management network)
sudo ufw allow 22222/udp   # RTPEngine

# Chặn access từ bên ngoài cho management ports
sudo ufw deny from any to any port 3307
sudo ufw deny from any to any port 6379
```

### SSL/TLS Setup
```bash
# Generate certificates
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Cấu hình trong opensips.cfg
listen=tls:0.0.0.0:5061
modparam("proto_tls", "certificate", "/etc/opensips/cert.pem")
modparam("proto_tls", "private_key", "/etc/opensips/key.pem")
```

### Password Security
```bash
# Thay đổi default passwords
# 1. MySQL root password
# 2. OpenSIPS database password
# 3. Control Panel admin password
# 4. FreeSWITCH event socket password
```

## Scaling

### Horizontal Scaling
```bash
# Multiple OpenSIPS instances
docker-compose up -d --scale opensips=3

# Load balancer configuration
# Sử dụng HAProxy hoặc Nginx upstream
```

### Vertical Scaling
```bash
# Tăng resources trong docker-compose.yml
services:
  opensips:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
```

## Disaster Recovery

### Backup Strategy
- Daily database backups
- Configuration file versioning
- Volume snapshots
- Off-site backup storage

### Recovery Procedures
```bash
# 1. Restore from backup
# 2. Verify configuration
# 3. Start services
# 4. Test functionality
# 5. Monitor for issues
```

## Useful Commands

### Docker Management
```bash
# Clean up unused resources
docker system prune -a

# View container details
docker inspect opensips_opensips_1

# Execute commands in container
docker-compose exec opensips bash
```

### OpenSIPS Commands
```bash
# Reload configuration (nếu hỗ trợ)
opensipsctl fifo reload

# Show statistics
opensipsctl fifo get_statistics all

# Show active dialogs
opensipsctl fifo dlg_list
```

---

**Lưu ý**: Tài liệu này sẽ được cập nhật thường xuyên khi có thêm kinh nghiệm vận hành.
