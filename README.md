# OpenSIPS CE - Hướng dẫn triển khai và cấu hình

## Giới thiệu về OpenSIPS CE

OpenSIPS (Open Session Initiation Protocol Server) Community Edition là một máy chủ SIP mã nguồn mở mạnh mẽ, đ<PERSON><PERSON><PERSON> thiết kế để xử lý các cuộc gọi VoIP và các dịch vụ viễn thông. OpenSIPS CE cung cấp một nền tảng SoftSwitch hoàn chỉnh với nhiều tính năng nâng cao.

### Tính năng chính của OpenSIPS CE

- **SIP Proxy**: Hỗ trợ TCP và UDP
- **Topology Hiding**: Ẩn cấu trúc mạng cho tất cả cuộc gọi
- **Dialplans**: Kế hoạch quay số cho người dùng và DID
- **Call Forward**: Chuyển tiếp cuộc gọi với nhiều tùy chọn
- **Access Control Lists (ACLs)**: <PERSON><PERSON>m soát truy cập chi tiết
- **RTPProxy**: Xử lý traffic voice
- **FreeSWITCH Integration**: Dịch vụ media và voicemail
- **Web Management Interface**: Giao diện quản lý qua web

### Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   SIP Clients   │────│   OpenSIPS      │────│   FreeSWITCH    │
│   (Softphones)  │    │   (SIP Proxy)   │    │   (Media Server)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   RTPProxy      │
                       │   (Media Relay) │
                       └─────────────────┘
                              │
                       ┌─────────────────┐
                       │   MySQL/MariaDB │
                       │   (Database)    │
                       └─────────────────┘
```

## Yêu cầu hệ thống

### Phần cứng tối thiểu
- CPU: 2 cores
- RAM: 4GB
- Storage: 20GB
- Network: 1Gbps

### Phần mềm
- Ubuntu 20.04/22.04 LTS hoặc CentOS 7/8
- Docker và Docker Compose (khuyến nghị)
- MySQL/MariaDB
- Apache/Nginx

## Phương pháp triển khai

### 1. Triển khai bằng Docker (Khuyến nghị)

Đây là cách nhanh nhất và dễ nhất để triển khai OpenSIPS CE.

### 2. Cài đặt từ source code

Phù hợp cho môi trường production và tùy chỉnh cao.

### 3. Cài đặt từ package

Sử dụng package manager của hệ điều hành.

## Bắt đầu nhanh với Docker

### Bước 1: Chuẩn bị môi trường

```bash
# Cập nhật hệ thống
sudo apt update && sudo apt upgrade -y

# Cài đặt Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Cài đặt Docker Compose
sudo apt install docker-compose -y

# Thêm user vào group docker
sudo usermod -aG docker $USER
```

### Bước 2: Clone repository

```bash
git clone --recursive https://github.com/OpenSIPS/opensips-softswitch-ce.git
cd opensips-softswitch-ce
```

### Bước 3: Cấu hình IP address

```bash
# Thay đổi IP address theo môi trường của bạn
MY_IP=*************
sed -i 's/HOST_IP=.*/HOST_IP='$MY_IP'/g' .env
```

### Bước 4: Khởi động services

```bash
docker-compose up -d
```

### Bước 5: Kiểm tra trạng thái

```bash
docker-compose ps
```

## Tài liệu dự án

### 📚 Tài liệu chính
- **[DEVELOPMENT.md](DEVELOPMENT.md)** - Tài liệu phát triển và kiến trúc hệ thống
- **[OPERATIONS.md](OPERATIONS.md)** - Hướng dẫn vận hành và troubleshooting
- **[TODO.md](TODO.md)** - Danh sách công việc và roadmap

### 🎯 Trạng thái dự án
- ✅ **Infrastructure**: Docker Compose setup hoàn tất
- ✅ **OpenSIPS**: Cấu hình cơ bản từ official template
- ✅ **Database**: MySQL setup với OpenSIPS schema
- ✅ **Media**: FreeSWITCH và RTPEngine integration
- 🔄 **Authentication**: Đang phát triển
- 🔄 **Testing**: Đang thực hiện

### 🚀 Quick Start
```bash
# Clone và khởi động
git clone <repository>
cd opensips-deployment
docker-compose up -d

# Kiểm tra services
docker-compose ps

# Xem logs
docker-compose logs opensips
```

### 🔗 Truy cập services
- **OpenSIPS SIP**: `udp://localhost:5060`
- **Control Panel**: `http://localhost:8081`
- **FreeSWITCH**: `sip://localhost:5090`
- **MySQL**: `localhost:3307`

### Bước 5: Truy cập Control Panel

- URL: http://localhost/cp
- Username: admin
- Password: opensips

## Cấu hình cơ bản

### 1. Cấu hình Database

OpenSIPS CE sử dụng MySQL/MariaDB để lưu trữ:
- Thông tin người dùng
- Dialplans
- CDR (Call Detail Records)
- ACLs

### 2. Cấu hình SIP Users

Thông qua Control Panel, bạn có thể:
- Tạo SIP accounts
- Cấu hình forwarding rules
- Thiết lập ACLs
- Quản lý DIDs

### 3. Cấu hình Dialplans

Dialplans định nghĩa cách xử lý các cuộc gọi:
- Local calls (nội bộ)
- PSTN calls (ra ngoài)
- Emergency calls
- Special codes

## Tính năng nâng cao

### 1. Load Balancing

OpenSIPS hỗ trợ load balancing cho:
- Multiple FreeSWITCH instances
- SIP trunks
- Media servers

### 2. High Availability

Cấu hình HA với:
- Database replication
- Shared storage
- Keepalived/Pacemaker

### 3. Security

- TLS/SRTP encryption
- Authentication mechanisms
- Rate limiting
- Fraud detection

### 4. Monitoring

- Homer SIP capture
- SNMP monitoring
- Log analysis
- Performance metrics

## Troubleshooting

### Các vấn đề thường gặp

1. **Audio issues**: Kiểm tra RTPProxy và firewall
2. **Registration failures**: Xác minh database và authentication
3. **Call routing**: Kiểm tra dialplans và ACLs
4. **Performance**: Monitor CPU, memory và network

### Log files

```bash
# OpenSIPS logs
tail -f /var/log/opensips.log

# FreeSWITCH logs
tail -f /var/log/freeswitch/freeswitch.log

# Docker logs
docker-compose logs -f opensips
```

## Tài liệu tham khảo

- [OpenSIPS Official Documentation](https://www.opensips.org/Documentation)
- [OpenSIPS Control Panel](https://controlpanel.opensips.org/)
- [FreeSWITCH Documentation](https://freeswitch.org/confluence/)
- [SIP RFC 3261](https://tools.ietf.org/html/rfc3261)

## Hỗ trợ

- [OpenSIPS Community Forum](https://forum.opensips.org/)
- [GitHub Issues](https://github.com/OpenSIPS/opensips-softswitch-ce/issues)
- [Commercial Support](https://www.opensips.org/Support/CommercialSupport)
