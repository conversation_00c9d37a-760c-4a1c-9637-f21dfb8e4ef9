version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: opensips_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: opensips_root_password
      MYSQL_DATABASE: opensips
      MYSQL_USER: opensips
      MYSQL_PASSWORD: opensips_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "3307:3306"
    healthcheck:
      test: mysqladmin ping -h localhost -u root -popensips_root_password
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      opensips_network:
        ipv4_address: ***********

  # OpenSIPS SIP Server
  opensips:
    image: opensips/opensips:softswitch-ce
    platform: linux/amd64
    container_name: opensips_server
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_USER=opensips
      - MYSQL_PASSWORD=opensips_password
      - MYSQL_DATABASE=opensips
      - SIP_DOMAIN=${SIP_DOMAIN:-localhost}
      - HOST_IP=${HOST_IP:-127.0.0.1}
    volumes:
      - ./opensips.cfg:/etc/opensips/opensips.cfg
      - ./opensips-cli.cfg:/etc/opensips/opensips-cli.cfg
      - opensips_logs:/var/log/opensips
      - opensips_fifo:/run/opensips  # Share FIFO with Control Panel
    ports:
      - "5060:5060/udp"
      - "5060:5060/tcp"
      - "8080:8080/tcp"  # HTTP MI interface
    networks:
      opensips_network:
        ipv4_address: ***********
    command: opensips -f /etc/opensips/opensips.cfg -D

  # RTPProxy for media handling
  rtpproxy:
    image: ghcr.io/sippy/rtpproxy:latest
    platform: linux/amd64
    container_name: opensips_rtpproxy
    restart: unless-stopped
    environment:
      - RTPPROXY_ADVERTISE_IP=***********
    ports:
      - "22222:22222/udp"
      - "30000-30100:30000-30100/udp"
    networks:
      opensips_network:
        ipv4_address: ***********
    command:
      - -l
      - ***********
      - -s
      - udp:***********:22222
      - -A
      - ***********
      - -m
      - "30000"
      - -M
      - "30100"
      - -F
      - -d
      - DBUG

  # FreeSWITCH CE for media services (Official OpenSIPS integration)
  freeswitch:
    image: opensips/freeswitch-ce:latest
    container_name: opensips_freeswitch
    restart: unless-stopped
    depends_on:
      - mysql
    environment:
      - MYSQL_HOST=mysql
      - MYSQL_USER=opensips
      - MYSQL_PASSWORD=opensips_password_2024
      - MYSQL_DATABASE=opensips
      - FREESWITCH_PASSWORD=${FREESWITCH_PASSWORD:-freeswitch_password_2024}
      - FREESWITCH_ESL_PASSWORD=${FREESWITCH_ESL_PASSWORD:-ClueCon}
      - HOST_IP=${HOST_IP:-***********}
    volumes:
      - freeswitch_conf:/etc/freeswitch
      - freeswitch_logs:/var/log/freeswitch
      - freeswitch_sounds:/usr/share/freeswitch/sounds
    ports:
      - "5090:5090/udp"  # SIP Internal
      - "5090:5090/tcp"  # SIP Internal
      - "8021:8021/tcp"  # Event Socket
      - "16384-16394:16384-16394/udp"  # RTP
    networks:
      opensips_network:
        ipv4_address: ***********

  # OpenSIPS Control Panel
  opensips-cp:
    image: opensips/opensips-cp:latest
    container_name: opensips_cp
    restart: unless-stopped
    depends_on:
      - mysql
      - opensips
    environment:
      - DB_HOST=mysql
      - DB_USER=opensips
      - DB_PASS=opensips_password
      - DB_NAME=opensips
      - OPENSIPS_HOST=opensips
      - OPENSIPS_MI_PORT=8080
    volumes:
      - opensips_fifo:/run/opensips  # Access FIFO from OpenSIPS
      - ./cp-config/db.inc.php:/var/www/html/opensips-cp/config/db.inc.php
    ports:
      - "8081:80"
    networks:
      opensips_network:
        ipv4_address: ***********



  # Redis for caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: opensips_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      opensips_network:
        ipv4_address: ***********

  # OpenSIPS UI Backend
  opensips-ui-backend:
    build:
      context: ./opensips-ui/backend
      dockerfile: Dockerfile
    container_name: opensips_ui_backend
    ports:
      - "3001:3001"
    depends_on:
      - opensips
      - mysql
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USER=opensips
      - DB_PASSWORD=opensips_password
      - DB_NAME=opensips
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_EXPIRES_IN=24h
      - OPENSIPS_MI_URL=http://opensips:8080/mi
      - OPENSIPS_HOST=opensips
      - OPENSIPS_PORT=5060
      - CORS_ORIGIN=http://***********:3000
    networks:
      opensips_network:
        ipv4_address: ***********
    restart: unless-stopped

  # OpenSIPS UI Frontend
  opensips-ui-frontend:
    build:
      context: ./opensips-ui/frontend
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=http://***********:3001
        - NEXT_PUBLIC_SOCKET_URL=http://***********:3001
    container_name: opensips_ui_frontend
    ports:
      - "3000:3000"
    depends_on:
      - opensips-ui-backend
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://***********:3001
      - NEXT_PUBLIC_SOCKET_URL=http://***********:3001
    networks:
      opensips_network:
        ipv4_address: ***********
    restart: unless-stopped



volumes:
  mysql_data:
    driver: local
  opensips_logs:
    driver: local
  opensips_fifo:
    driver: local
  freeswitch_conf:
    driver: local
  freeswitch_sounds:
    driver: local
  freeswitch_logs:
    driver: local
  redis_data:
    driver: local

networks:
  opensips_network:
    name: opensips_network
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: opensips-br
    ipam:
      config:
        - subnet: **********/16
