<?xml version="1.0" encoding="utf-8"?>
<configuration name="acl.conf" description="Network Lists">
  <network-lists>
    
    <!-- OpenSIPS Integration ACL -->
    <list name="opensips" default="deny">
      <!-- Allow OpenSIPS server -->
      <node type="allow" cidr="***********/32"/>
      <!-- Allow local network -->
      <node type="allow" cidr="**********/16"/>
      <!-- Allow localhost -->
      <node type="allow" cidr="127.0.0.1/32"/>
    </list>

    <!-- Domain ACL for directory authentication -->
    <list name="domains" default="deny">
      <!-- Allow OpenSIPS domain -->
      <node type="allow" domain="opensips.local"/>
      <!-- Allow OpenSIPS server IP -->
      <node type="allow" cidr="***********/32"/>
      <!-- Allow Docker network -->
      <node type="allow" cidr="**********/16"/>
    </list>

    <!-- LAN ACL -->
    <list name="lan" default="allow">
      <node type="deny" cidr="************/24"/>
      <node type="allow" cidr="*************/32"/>
    </list>

    <!-- Trusted networks -->
    <list name="trusted" default="deny">
      <!-- OpenSIPS server -->
      <node type="allow" cidr="***********/32"/>
      <!-- Docker internal network -->
      <node type="allow" cidr="**********/16"/>
      <!-- Local loopback -->
      <node type="allow" cidr="127.0.0.1/32"/>
    </list>

  </network-lists>
</configuration>
