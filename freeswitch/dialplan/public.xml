<?xml version="1.0" encoding="utf-8"?>
<!-- FreeSWITCH Public Dialplan for OpenSIPS Integration -->
<include>
  <context name="public">
    
    <!-- Anti-flood protection -->
    <extension name="unloop">
      <condition field="${unroll_loops}" expression="^true$"/>
      <condition field="${sip_looped_call}" expression="^true$">
        <action application="deflect" data="sip:${destination_number}@${sip_req_host}"/>
      </condition>
    </extension>

    <!-- Calls from OpenSIPS -->
    <extension name="opensips_calls">
      <condition field="network_addr" expression="^42\.96\.20\.37$" break="never">
        <action application="set" data="domain_name=opensips.local"/>
        <action application="set" data="domain_uuid="/>
      </condition>
      <condition field="destination_number" expression="^(.*)$">
        <action application="set" data="domain_name=opensips.local"/>
        <action application="transfer" data="$1 XML default"/>
      </condition>
    </extension>

    <!-- Voicemail Access -->
    <extension name="voicemail">
      <condition field="destination_number" expression="^(\*97|\*98)$">
        <action application="answer"/>
        <action application="sleep" data="1000"/>
        <action application="voicemail" data="check default opensips.local"/>
      </condition>
    </extension>

    <!-- Conference Access -->
    <extension name="conference">
      <condition field="destination_number" expression="^(\*3\d{3,4})$">
        <action application="answer"/>
        <action application="conference" data="$1@default"/>
      </condition>
    </extension>

    <!-- Music on Hold Test -->
    <extension name="music_on_hold">
      <condition field="destination_number" expression="^(\*9664)$">
        <action application="answer"/>
        <action application="playback" data="local_stream://moh"/>
      </condition>
    </extension>

    <!-- Echo Test -->
    <extension name="echo_test">
      <condition field="destination_number" expression="^(\*9196)$">
        <action application="answer"/>
        <action application="echo"/>
      </condition>
    </extension>

    <!-- Time and Date -->
    <extension name="time_date">
      <condition field="destination_number" expression="^(\*9197)$">
        <action application="answer"/>
        <action application="say" data="en current_date_time pronounced ${strepoch()}"/>
      </condition>
    </extension>

    <!-- Feature Codes with * prefix -->
    <extension name="feature_codes">
      <condition field="destination_number" expression="^(\*\d+)$">
        <action application="set" data="feature_code=${destination_number:1}"/>
        <action application="transfer" data="${feature_code} XML features"/>
      </condition>
    </extension>

    <!-- Default: Send to OpenSIPS for routing -->
    <extension name="opensips_routing">
      <condition field="destination_number" expression="^(.*)$">
        <action application="set" data="hangup_after_bridge=true"/>
        <action application="set" data="continue_on_fail=true"/>
        <action application="bridge" data="sofia/external/$1@***********:5060"/>
      </condition>
    </extension>

    <!-- Catch-all for unmatched calls -->
    <extension name="catchall">
      <condition field="destination_number" expression="^.*$">
        <action application="answer"/>
        <action application="sleep" data="1000"/>
        <action application="playback" data="ivr/ivr-that_was_an_invalid_entry.wav"/>
        <action application="hangup"/>
      </condition>
    </extension>

  </context>
</include>
