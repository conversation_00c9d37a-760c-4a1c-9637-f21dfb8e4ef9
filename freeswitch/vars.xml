<include>
  <!-- FreeSWITCH Variables for OpenSIPS Integration -->
  
  <!-- Basic Settings -->
  <X-PRE-PROCESS cmd="set" data="default_password=1234"/>
  <X-PRE-PROCESS cmd="set" data="sound_prefix=$${sounds_dir}/en/us/callie"/>
  
  <!-- Domain Configuration -->
  <X-PRE-PROCESS cmd="set" data="domain=opensips.local"/>
  <X-PRE-PROCESS cmd="set" data="domain_name=$${domain}"/>
  <X-PRE-PROCESS cmd="set" data="hold_music=local_stream://moh"/>
  <X-PRE-PROCESS cmd="set" data="use_profile=internal"/>
  
  <!-- Security -->
  <X-PRE-PROCESS cmd="set" data="zrtp_secure_media=true"/>
  
  <!-- Codec Settings -->
  <X-PRE-PROCESS cmd="set" data="global_codec_prefs=G722,PCMU,PCMA,GSM"/>
  <X-PRE-PROCESS cmd="set" data="outbound_codec_prefs=PCMU,PCMA,GSM"/>
  
  <!-- Network Settings -->
  <X-PRE-PROCESS cmd="set" data="bind_server_ip=auto"/>
  <X-PRE-PROCESS cmd="set" data="external_rtp_ip=***********"/>
  <X-PRE-PROCESS cmd="set" data="external_sip_ip=***********"/>
  <X-PRE-PROCESS cmd="set" data="unroll_loops=true"/>
  
  <!-- Caller ID -->
  <X-PRE-PROCESS cmd="set" data="outbound_caller_name=OpenSIPS-FreeSWITCH"/>
  <X-PRE-PROCESS cmd="set" data="outbound_caller_id=0000000000"/>
  
  <!-- Debug Settings -->
  <X-PRE-PROCESS cmd="set" data="call_debug=false"/>
  <X-PRE-PROCESS cmd="set" data="console_loglevel=info"/>
  <X-PRE-PROCESS cmd="set" data="default_areacode=084"/>
  <X-PRE-PROCESS cmd="set" data="default_country=VN"/>
  
  <!-- Privacy -->
  <X-PRE-PROCESS cmd="set" data="presence_privacy=false"/>
  
  <!-- SIP Profiles - Configured for OpenSIPS Integration -->
  <!-- Internal Profile (for OpenSIPS communication) -->
  <X-PRE-PROCESS cmd="set" data="internal_auth_calls=false"/>
  <X-PRE-PROCESS cmd="set" data="internal_sip_port=5090"/>
  <X-PRE-PROCESS cmd="set" data="internal_tls_port=5061"/>
  <X-PRE-PROCESS cmd="set" data="internal_ssl_enable=false"/>
  <X-PRE-PROCESS cmd="set" data="internal_ssl_dir=$${base_dir}/conf/ssl"/>
  
  <!-- External Profile (for external SIP providers) -->
  <X-PRE-PROCESS cmd="set" data="external_auth_calls=false"/>
  <X-PRE-PROCESS cmd="set" data="external_sip_port=5091"/>
  <X-PRE-PROCESS cmd="set" data="external_tls_port=5081"/>
  <X-PRE-PROCESS cmd="set" data="external_ssl_enable=false"/>
  <X-PRE-PROCESS cmd="set" data="external_ssl_dir=$${base_dir}/conf/ssl"/>
  
  <!-- Ring Tones -->
  <X-PRE-PROCESS cmd="set" data="us-ring=%(2000,4000,440,480)"/>
  <X-PRE-PROCESS cmd="set" data="vn-ring=%(1000,4000,425)"/>
  <X-PRE-PROCESS cmd="set" data="sit=%(274,0,913.8);%(274,0,1370.6);%(380,0,1776.7)"/>
  
  <!-- Provider Settings -->
  <X-PRE-PROCESS cmd="set" data="default_provider=opensips.local"/>
  <X-PRE-PROCESS cmd="set" data="default_provider_username=freeswitch"/>
  <X-PRE-PROCESS cmd="set" data="default_provider_password=freeswitch_password_2024"/>
  <X-PRE-PROCESS cmd="set" data="default_provider_from_domain=opensips.local"/>
  <X-PRE-PROCESS cmd="set" data="default_provider_register=false"/>
  <X-PRE-PROCESS cmd="set" data="default_provider_contact=5000"/>
  
  <!-- TLS Settings -->
  <X-PRE-PROCESS cmd="set" data="sip_tls_version=tlsv1"/>
</include>
