#!/bin/bash

# OpenSIPS CE Installation Script
# Supports Ubuntu 20.04/22.04 and CentOS 7/8

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}" >&2
}

warning() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root"
        exit 1
    fi
}

# Detect OS
detect_os() {
    if [[ -f /etc/os-release ]]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        error "Cannot detect OS"
        exit 1
    fi
    
    log "Detected OS: $OS $VER"
}

# Install Docker
install_docker() {
    log "Installing Docker..."
    
    if command -v docker &> /dev/null; then
        info "Docker is already installed"
        return
    fi
    
    case $OS in
        "Ubuntu")
            sudo apt-get update
            sudo apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
            curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
            echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
            sudo apt-get update
            sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            ;;
        "CentOS Linux")
            sudo yum install -y yum-utils
            sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
            sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
            sudo systemctl start docker
            sudo systemctl enable docker
            ;;
        *)
            error "Unsupported OS: $OS"
            exit 1
            ;;
    esac
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    log "Docker installed successfully"
}

# Install Docker Compose
install_docker_compose() {
    log "Installing Docker Compose..."
    
    if command -v docker-compose &> /dev/null; then
        info "Docker Compose is already installed"
        return
    fi
    
    case $OS in
        "Ubuntu")
            sudo apt-get install -y docker-compose
            ;;
        "CentOS Linux")
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
            ;;
    esac
    
    log "Docker Compose installed successfully"
}

# Configure firewall
configure_firewall() {
    log "Configuring firewall..."
    
    case $OS in
        "Ubuntu")
            if command -v ufw &> /dev/null; then
                sudo ufw allow 5060/udp comment "OpenSIPS SIP"
                sudo ufw allow 5060/tcp comment "OpenSIPS SIP"
                sudo ufw allow 80/tcp comment "HTTP"
                sudo ufw allow 443/tcp comment "HTTPS"
                sudo ufw allow 35000:35100/udp comment "RTP"
                sudo ufw allow 9060/udp comment "Homer HEP"
                info "UFW rules added"
            fi
            ;;
        "CentOS Linux")
            if command -v firewall-cmd &> /dev/null; then
                sudo firewall-cmd --permanent --add-port=5060/udp
                sudo firewall-cmd --permanent --add-port=5060/tcp
                sudo firewall-cmd --permanent --add-port=80/tcp
                sudo firewall-cmd --permanent --add-port=443/tcp
                sudo firewall-cmd --permanent --add-port=35000-35100/udp
                sudo firewall-cmd --permanent --add-port=9060/udp
                sudo firewall-cmd --reload
                info "Firewall rules added"
            fi
            ;;
    esac
}

# Get network configuration
get_network_config() {
    log "Detecting network configuration..."
    
    # Get primary IP address
    HOST_IP=$(ip route get ******* | awk '{print $7; exit}')
    
    if [[ -z "$HOST_IP" ]]; then
        warning "Could not detect IP address automatically"
        read -p "Please enter your server IP address: " HOST_IP
    fi
    
    log "Using IP address: $HOST_IP"
    
    # Update .env file
    sed -i "s/HOST_IP=.*/HOST_IP=$HOST_IP/g" .env
    sed -i "s/PUBLIC_IP=.*/PUBLIC_IP=$HOST_IP/g" .env
}

# Create directories
create_directories() {
    log "Creating directories..."
    
    mkdir -p ssl
    mkdir -p sql
    mkdir -p cp-config
    mkdir -p logs
    
    # Generate self-signed SSL certificate
    if [[ ! -f ssl/cert.pem ]]; then
        log "Generating SSL certificate..."
        openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=$HOST_IP"
    fi
}

# Download OpenSIPS configuration
download_config() {
    log "Downloading OpenSIPS configuration..."
    
    if [[ ! -f opensips.cfg ]]; then
        curl -o opensips.cfg https://raw.githubusercontent.com/OpenSIPS/opensips-softswitch-ce-config/main/opensips.cfg
    fi
    
    if [[ ! -f opensips-cli.cfg ]]; then
        curl -o opensips-cli.cfg https://raw.githubusercontent.com/OpenSIPS/opensips-softswitch-ce-config/main/opensips-cli.cfg
    fi
}

# Start services
start_services() {
    log "Starting OpenSIPS CE services..."
    
    # Pull images
    docker-compose pull
    
    # Start services
    docker-compose up -d
    
    log "Waiting for services to start..."
    sleep 30
    
    # Check service status
    docker-compose ps
}

# Post-installation setup
post_install() {
    log "Running post-installation setup..."
    
    # Wait for MySQL to be ready
    log "Waiting for MySQL to be ready..."
    until docker-compose exec mysql mysqladmin ping -h"localhost" --silent; do
        sleep 2
    done
    
    # Initialize OpenSIPS database
    log "Initializing OpenSIPS database..."
    docker-compose exec opensips opensips-cli -x database create
    
    # Create admin user for Control Panel
    log "Creating admin user..."
    docker-compose exec mysql mysql -u opensips -popensips_password_2024 opensips -e "
        INSERT INTO subscriber (username, domain, password, ha1) VALUES 
        ('admin', '$HOST_IP', 'opensips', MD5('admin:$HOST_IP:opensips'));
    "
    
    log "OpenSIPS CE installation completed successfully!"
    info "Access the Control Panel at: http://$HOST_IP/cp"
    info "Username: admin"
    info "Password: opensips"
}

# Main installation function
main() {
    log "Starting OpenSIPS CE installation..."
    
    check_root
    detect_os
    install_docker
    install_docker_compose
    configure_firewall
    get_network_config
    create_directories
    download_config
    start_services
    post_install
    
    log "Installation completed successfully!"
    warning "Please log out and log back in for Docker group changes to take effect"
}

# Run main function
main "$@"
