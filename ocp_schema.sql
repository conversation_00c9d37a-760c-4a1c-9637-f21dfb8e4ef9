CREATE TABLE `ocp_admin_privileges` (
  `id` int(10) unsigned NOT NULL auto_increment,
  `first_name` varchar(64) NOT NULL default '',
  `last_name` varchar(64) NOT NULL default '',
  `username` varchar(64) NOT NULL default '',
  `password` varchar(64) NOT NULL default '',
  `ha1` varchar(256) default '',
  `blocked` varchar(60) default NULL,
  `failed_attempts` int default 0,
  `available_tools` varchar(512) NOT NULL default '',
  `permissions` varchar(512) default NULL,
  `secret` varchar(20) default NULL,
  PRIMARY KEY (`id`)
);

INSERT INTO ocp_admin_privileges (username,password,first_name,last_name,ha1,available_tools,permissions) values ('admin','opensips','Super','Admin',md5('admin:opensips'),'all','all');

-- Table for `ocp_monitored_stats`
DROP TABLE IF EXISTS `ocp_monitored_stats`;
CREATE TABLE `ocp_monitored_stats` (
  `name` varchar(64) NOT NULL,
  `box_id` mediumint(8) unsigned NOT NULL default '0',
  PRIMARY KEY (`name`,`box_id`)
);

-- Table for `ocp_monitoring_stats`
DROP TABLE IF EXISTS `ocp_monitoring_stats`;
CREATE TABLE `ocp_monitoring_stats` (
  `name` varchar(64) NOT NULL,
  `time` int(11) NOT NULL,
  `value` varchar(64) NOT NULL default '0',
  `box_id` mediumint(8) unsigned NOT NULL default '0',
  PRIMARY KEY (`name`,`time`,`box_id`)
);

-- Table for `ocp_boxes_config`
CREATE TABLE `ocp_boxes_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) NOT NULL DEFAULT '',
  `mi_conn` varchar(64) DEFAULT NULL,
  `monit_conn` varchar(64) DEFAULT NULL,
  `monit_user` varchar(64) DEFAULT NULL,
  `monit_pass` varchar(64) DEFAULT NULL,
  `monit_ssl` tinyint NOT NULL DEFAULT 0,
  `smonitcharts` tinyint NOT NULL DEFAULT 1,
  `assoc_id` varchar(10) DEFAULT '-1',
  `desc` varchar(128) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name_key` (`name`)
);

INSERT INTO ocp_boxes_config (mi_conn,`desc`,assoc_id) values ('json:opensips:8080/mi','Default box',1);

-- Table for `ocp_system_config`
CREATE TABLE `ocp_system_config` (
  `assoc_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(64) DEFAULT NULL,
  `desc` varchar(64) DEFAULT '',
  PRIMARY KEY (`assoc_id`)
);

INSERT INTO ocp_system_config (assoc_id, name, `desc`) values (1,'System 0','Default system');

-- Table for `ocp_tools_config`
CREATE TABLE `ocp_tools_config` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `module` varchar(64) NOT NULL,
  `param` varchar(64) NOT NULL,
  `value` blob DEFAULT NULL,
  `box_id` varchar(15) DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `box_key` (`module`,`param`,`box_id`)
);

-- Table for `ocp_db_config`
CREATE TABLE `ocp_db_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_name` varchar(64) NOT NULL,
  `db_host` varchar(64) NOT NULL,
  `db_port` varchar(64) NOT NULL,
  `db_user` varchar(64) NOT NULL,
  `db_pass` varchar(64) DEFAULT NULL,
  `db_name` varchar(64) NOT NULL,
  PRIMARY KEY (`id`)
);

-- Table for `config` tool
CREATE TABLE `config` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  `description` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
);
