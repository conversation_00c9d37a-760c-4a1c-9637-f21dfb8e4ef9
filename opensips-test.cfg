####### Global Parameters #########

log_level=3
stderror_enabled=no
children=4

listen=udp:0.0.0.0:5060

####### Modules Section ########

# set module path
mpath="/usr/lib/x86_64-linux-gnu/opensips/modules/"

#### SIGNALING module
loadmodule "signaling.so"

#### StateLess module
loadmodule "sl.so"

#### Transaction Module
loadmodule "tm.so"

#### Record Route Module
loadmodule "rr.so"
modparam("rr", "append_fromtag", 0)

#### SIP MSG OPerationS module
loadmodule "sipmsgops.so"

#### USeR LOCation module (memory only for testing)
loadmodule "usrloc.so"

#### REGISTRAR module
loadmodule "registrar.so"

####### Routing Logic ########

# main request routing logic
route{
	if (has_totag()) {
		loose_route();
		route(relay);
	}

	if (from_uri!=myself && uri!=myself) {
		send_reply(403,"Relay forbidden");
		exit;
	}

	if (is_method("REGISTER")) {
		# Simple registration without authentication for testing
		if (!save("location"))
			xlog("failed to register AoR $tu\n");
		exit;
	}

	record_route();
	route(relay);
}

route[relay] {
	if (is_method("INVITE"))
		t_on_failure("missed_call");

	t_relay();
	exit;
}

failure_route[missed_call] {
	if (t_check_status("486")) {
		$rd = "127.0.0.1";
		t_relay();
	}
}
