"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _defineProperty;
var _toPropertyKey = require("./toPropertyKey.js");
function _defineProperty(obj, key, value) {
  key = (0, _toPropertyKey.default)(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}

//# sourceMappingURL=defineProperty.js.map
