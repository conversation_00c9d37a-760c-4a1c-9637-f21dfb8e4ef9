# @babel/plugin-syntax-numeric-separator

> Allow parsing of Decimal, Binary, Hex and Octal literals that contain a Numeric Literal Separator

See our website [@babel/plugin-syntax-numeric-separator](https://babeljs.io/docs/en/next/babel-plugin-syntax-numeric-separator.html) for more information.

## Install

Using npm:

```sh
npm install --save-dev @babel/plugin-syntax-numeric-separator
```

or using yarn:

```sh
yarn add @babel/plugin-syntax-numeric-separator --dev
```
