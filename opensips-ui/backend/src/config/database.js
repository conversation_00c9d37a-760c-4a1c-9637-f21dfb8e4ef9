const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'opensips',
  password: process.env.DB_PASSWORD || 'opensips_password',
  database: process.env.DB_NAME || 'opensips',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

// Create connection pool
const pool = mysql.createPool(dbConfig);

// Test connection
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('✅ Database connected successfully');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
};

// Execute query with error handling
const executeQuery = async (query, params = []) => {
  try {
    const [rows] = await pool.execute(query, params);
    return { success: true, data: rows };
  } catch (error) {
    console.error('Database query error:', error);
    return { success: false, error: error.message };
  }
};

// Get database statistics
const getStats = async () => {
  try {
    const queries = [
      'SELECT COUNT(*) as total_users FROM subscriber',
      'SELECT COUNT(*) as active_registrations FROM location WHERE expires > UNIX_TIMESTAMP()',
      'SELECT COUNT(*) as total_calls FROM acc WHERE method = "INVITE"',
      'SELECT COUNT(*) as active_dialogs FROM dialog'
    ];

    const results = await Promise.all(
      queries.map(query => executeQuery(query))
    );

    return {
      totalUsers: results[0].success ? results[0].data[0].total_users : 0,
      activeRegistrations: results[1].success ? results[1].data[0].active_registrations : 0,
      totalCalls: results[2].success ? results[2].data[0].total_calls : 0,
      activeDialogs: results[3].success ? results[3].data[0].active_dialogs : 0
    };
  } catch (error) {
    console.error('Error getting database stats:', error);
    return {
      totalUsers: 0,
      activeRegistrations: 0,
      totalCalls: 0,
      activeDialogs: 0
    };
  }
};

module.exports = {
  pool,
  testConnection,
  executeQuery,
  getStats
};
