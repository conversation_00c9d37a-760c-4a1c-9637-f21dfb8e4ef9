const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-change-in-production';

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      console.log('🔐 Token verification failed:', err.message);
      return res.status(403).json({
        success: false,
        message: 'Invalid or expired token'
      });
    }

    console.log('🔐 Token verified for user:', user.username);
    req.user = user;
    next();
  });
};

// Permission-based authorization middleware
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: 'Authentication required'
        });
      }

      console.log('🔍 Checking permission:', permission, 'for user:', req.user.username);

      // Check if user is admin first (admin has all permissions)
      if (req.user.role === 'admin') {
        console.log('✅ Admin user - permission granted');
        next();
        return;
      }

      // Get user permissions from database
      const [users] = await pool.execute(
        'SELECT permissions FROM ocp_admin_privileges WHERE id = ?',
        [req.user.userId]
      );

      if (users.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'User not found'
        });
      }

      // Parse permissions (could be JSON array or comma-separated string)
      let userPermissions = [];
      const permissionsData = users[0].permissions;

      if (permissionsData) {
        if (permissionsData === 'all') {
          // User has all permissions
          console.log('✅ User has all permissions');
          next();
          return;
        }

        try {
          userPermissions = JSON.parse(permissionsData);
        } catch (e) {
          // If not JSON, treat as comma-separated string
          userPermissions = permissionsData.split(',').map(p => p.trim());
        }
      }

      console.log('🔍 User permissions:', userPermissions);

      // Check if user has the required permission
      if (userPermissions.includes(permission)) {
        console.log('✅ Permission granted');
        next();
      } else {
        console.log('❌ Permission denied');
        res.status(403).json({
          success: false,
          message: `Insufficient permissions. Required: ${permission}`
        });
      }
    } catch (error) {
      console.error('❌ Permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Permission check failed'
      });
    }
  };
};

module.exports = {
  authenticateToken,
  requirePermission
};
