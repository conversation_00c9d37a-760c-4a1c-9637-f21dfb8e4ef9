const express = require('express');
const router = express.Router();
const { authenticateToken, requirePermission } = require('../middleware/auth');
const pool = require('../config/database');

// Permission middleware for calls
const requireCallsRead = requirePermission('calls.read');
const requireCallsWrite = requirePermission('calls.write');

// Get call history with pagination and filtering
router.get('/history', authenticateToken, requireCallsRead, async (req, res) => {
  try {
    console.log('🔍 Getting call history...');
    
    const {
      page = 1,
      limit = 20,
      from_date,
      to_date,
      caller,
      callee,
      status,
      method = 'INVITE'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);
    
    // Build WHERE clause
    let whereConditions = ['method = ?'];
    let queryParams = [method];
    
    if (from_date) {
      whereConditions.push('time >= ?');
      queryParams.push(from_date);
    }
    
    if (to_date) {
      whereConditions.push('time <= ?');
      queryParams.push(to_date);
    }
    
    if (caller) {
      whereConditions.push('from_tag LIKE ?');
      queryParams.push(`%${caller}%`);
    }
    
    if (callee) {
      whereConditions.push('to_tag LIKE ?');
      queryParams.push(`%${callee}%`);
    }
    
    if (status) {
      whereConditions.push('sip_code = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    // Get call history
    const query = `
      SELECT 
        id,
        method,
        from_tag as caller,
        to_tag as callee,
        callid,
        sip_code,
        sip_reason,
        time,
        duration,
        ms_duration,
        setuptime,
        created
      FROM acc
      ${whereClause}
      ORDER BY time DESC
      LIMIT ? OFFSET ?
    `;

    console.log('🔍 Executing query:', query);
    console.log('🔍 Query params:', [...queryParams, parseInt(limit), offset]);
    
    const [calls] = await pool.execute(query, [...queryParams, parseInt(limit), offset]);
    
    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM acc ${whereClause}`;
    const [countResult] = await pool.execute(countQuery, queryParams);
    const total = countResult[0].total;
    
    console.log('🔍 Found calls:', calls.length, 'Total:', total);

    // Process calls data
    const processedCalls = calls.map(call => ({
      id: call.id,
      callId: call.callid,
      caller: call.caller,
      callee: call.callee,
      method: call.method,
      status: {
        code: call.sip_code,
        reason: call.sip_reason
      },
      time: call.time,
      duration: call.duration,
      setupTime: call.setuptime,
      created: call.created,
      isSuccessful: call.sip_code === '200'
    }));

    res.json({
      success: true,
      data: {
        calls: processedCalls,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('❌ Error getting call history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get call history',
      error: error.message
    });
  }
});

// Get active calls (dialogs)
router.get('/active', authenticateToken, requireCallsRead, async (req, res) => {
  try {
    console.log('🔍 Getting active calls...');
    
    const query = `
      SELECT 
        dlg_id,
        callid,
        from_uri,
        from_tag,
        to_uri,
        to_tag,
        start_time,
        state,
        timeout,
        caller_contact,
        callee_contact
      FROM dialog
      WHERE state = 4
      ORDER BY start_time DESC
    `;

    const [dialogs] = await pool.execute(query);
    console.log('🔍 Found active dialogs:', dialogs.length);

    // Process dialogs data
    const activeCalls = dialogs.map(dialog => {
      const startTime = new Date(dialog.start_time * 1000);
      const duration = Math.floor((Date.now() - startTime.getTime()) / 1000);
      
      return {
        id: dialog.dlg_id,
        callId: dialog.callid,
        caller: {
          uri: dialog.from_uri,
          tag: dialog.from_tag,
          contact: dialog.caller_contact
        },
        callee: {
          uri: dialog.to_uri,
          tag: dialog.to_tag,
          contact: dialog.callee_contact
        },
        startTime: startTime.toISOString(),
        duration,
        state: dialog.state,
        timeout: dialog.timeout
      };
    });

    res.json({
      success: true,
      data: {
        activeCalls,
        count: activeCalls.length
      }
    });

  } catch (error) {
    console.error('❌ Error getting active calls:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get active calls',
      error: error.message
    });
  }
});

// Get call statistics
router.get('/stats', authenticateToken, requireCallsRead, async (req, res) => {
  try {
    console.log('🔍 Getting call statistics...');
    
    const { period = '24h' } = req.query;
    
    // Calculate time range based on period
    let timeCondition = '';
    const now = new Date();
    
    switch (period) {
      case '1h':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)`;
        break;
      case '24h':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`;
        break;
      case '7d':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 7 DAY)`;
        break;
      case '30d':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 30 DAY)`;
        break;
      default:
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`;
    }

    // Get various statistics
    const queries = {
      total: `SELECT COUNT(*) as count FROM acc WHERE method = 'INVITE' AND ${timeCondition}`,
      successful: `SELECT COUNT(*) as count FROM acc WHERE method = 'INVITE' AND sip_code = '200' AND ${timeCondition}`,
      failed: `SELECT COUNT(*) as count FROM acc WHERE method = 'INVITE' AND sip_code != '200' AND ${timeCondition}`,
      avgDuration: `SELECT AVG(duration) as avg_duration FROM acc WHERE method = 'INVITE' AND sip_code = '200' AND duration > 0 AND ${timeCondition}`,
      totalDuration: `SELECT SUM(duration) as total_duration FROM acc WHERE method = 'INVITE' AND sip_code = '200' AND ${timeCondition}`,
      activeCalls: `SELECT COUNT(*) as count FROM dialog WHERE state = 4`
    };

    const results = {};
    for (const [key, query] of Object.entries(queries)) {
      const [result] = await pool.execute(query);
      results[key] = result[0];
    }

    // Calculate success rate
    const successRate = results.total.count > 0 
      ? ((results.successful.count / results.total.count) * 100).toFixed(2)
      : 0;

    const stats = {
      period,
      totalCalls: results.total.count,
      successfulCalls: results.successful.count,
      failedCalls: results.failed.count,
      successRate: parseFloat(successRate),
      activeCalls: results.activeCalls.count,
      averageDuration: Math.round(results.avgDuration.avg_duration || 0),
      totalDuration: results.totalDuration.total_duration || 0
    };

    console.log('📊 Call statistics:', stats);

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Error getting call statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get call statistics',
      error: error.message
    });
  }
});

// Hangup active call
router.post('/hangup/:callId', authenticateToken, requireCallsWrite, async (req, res) => {
  try {
    const { callId } = req.params;
    console.log('🔌 Hanging up call:', callId);

    // First check if dialog exists
    const [dialogs] = await pool.execute(
      'SELECT dlg_id, callid FROM dialog WHERE callid = ? AND state = 4',
      [callId]
    );

    if (dialogs.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Active call not found'
      });
    }

    // Use OpenSIPS MI interface to hangup call
    const miService = require('../services/miService');
    const result = await miService.executeCommand('dlg_end_dlg', {
      callid: callId
    });

    if (result.success) {
      console.log('✅ Call hung up successfully');
      res.json({
        success: true,
        message: 'Call hung up successfully'
      });
    } else {
      console.log('❌ Failed to hang up call:', result.error);
      res.status(500).json({
        success: false,
        message: 'Failed to hang up call',
        error: result.error
      });
    }

  } catch (error) {
    console.error('❌ Error hanging up call:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to hang up call',
      error: error.message
    });
  }
});

// Transfer active call
router.post('/transfer/:callId', authenticateToken, requireCallsWrite, async (req, res) => {
  try {
    const { callId } = req.params;
    const { transferTo } = req.body;

    console.log('🔄 Transferring call:', callId, 'to:', transferTo);

    if (!transferTo) {
      return res.status(400).json({
        success: false,
        message: 'Transfer destination is required'
      });
    }

    // Check if dialog exists
    const [dialogs] = await pool.execute(
      'SELECT dlg_id, callid FROM dialog WHERE callid = ? AND state = 4',
      [callId]
    );

    if (dialogs.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Active call not found'
      });
    }

    // Use OpenSIPS MI interface to transfer call
    const miService = require('../services/miService');
    const result = await miService.executeCommand('dlg_refer', {
      callid: callId,
      refer_to: transferTo
    });

    if (result.success) {
      console.log('✅ Call transferred successfully');
      res.json({
        success: true,
        message: 'Call transferred successfully'
      });
    } else {
      console.log('❌ Failed to transfer call:', result.error);
      res.status(500).json({
        success: false,
        message: 'Failed to transfer call',
        error: result.error
      });
    }

  } catch (error) {
    console.error('❌ Error transferring call:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to transfer call',
      error: error.message
    });
  }
});

// Get missed calls
router.get('/missed', authenticateToken, requireCallsRead, async (req, res) => {
  try {
    console.log('🔍 Getting missed calls...');

    const {
      page = 1,
      limit = 20,
      from_date,
      to_date,
      callee
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Build WHERE clause
    let whereConditions = ['method = ?'];
    let queryParams = ['INVITE'];

    if (from_date) {
      whereConditions.push('time >= ?');
      queryParams.push(from_date);
    }

    if (to_date) {
      whereConditions.push('time <= ?');
      queryParams.push(to_date);
    }

    if (callee) {
      whereConditions.push('to_tag LIKE ?');
      queryParams.push(`%${callee}%`);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Get missed calls
    const query = `
      SELECT
        id,
        method,
        from_tag as caller,
        to_tag as callee,
        callid,
        sip_code,
        sip_reason,
        time,
        setuptime,
        created
      FROM missed_calls
      ${whereClause}
      ORDER BY time DESC
      LIMIT ? OFFSET ?
    `;

    const [calls] = await pool.execute(query, [...queryParams, parseInt(limit), offset]);

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM missed_calls ${whereClause}`;
    const [countResult] = await pool.execute(countQuery, queryParams);
    const total = countResult[0].total;

    console.log('🔍 Found missed calls:', calls.length, 'Total:', total);

    // Process missed calls data
    const processedCalls = calls.map(call => ({
      id: call.id,
      callId: call.callid,
      caller: call.caller,
      callee: call.callee,
      method: call.method,
      status: {
        code: call.sip_code,
        reason: call.sip_reason
      },
      time: call.time,
      setupTime: call.setuptime,
      created: call.created
    }));

    res.json({
      success: true,
      data: {
        missedCalls: processedCalls,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages: Math.ceil(total / parseInt(limit))
        }
      }
    });

  } catch (error) {
    console.error('❌ Error getting missed calls:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get missed calls',
      error: error.message
    });
  }
});

module.exports = router;
