const express = require('express');
const router = express.Router();
const MIService = require('../services/mi');

// Initialize MI service
const miService = new MIService();

// Test MI connection
router.get('/test', async (req, res) => {
  try {
    const connected = await miService.testConnection();
    const info = miService.getInfo();
    
    res.json({
      success: true,
      connected,
      info,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ MI test error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get system status via MI
router.get('/status', async (req, res) => {
  try {
    const status = await miService.getSystemStatus();
    res.json({
      success: true,
      status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ MI status error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get location statistics
router.get('/location/stats', async (req, res) => {
  try {
    const stats = await miService.getLocationStatistics();
    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ MI location stats error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get dialog statistics
router.get('/dialog/stats', async (req, res) => {
  try {
    const stats = await miService.getDialogStatistics();
    res.json({
      success: true,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ MI dialog stats error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get all users via MI
router.get('/users', async (req, res) => {
  try {
    const users = await miService.getAllUsers();
    res.json({
      success: true,
      users,
      count: users.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ MI users error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get all dialogs via MI
router.get('/dialogs', async (req, res) => {
  try {
    const dialogs = await miService.getAllDialogs();
    res.json({
      success: true,
      dialogs,
      count: dialogs.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ MI dialogs error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Execute custom MI command
router.post('/command', async (req, res) => {
  try {
    const { command, params = [] } = req.body;
    
    if (!command) {
      return res.status(400).json({
        success: false,
        error: 'Command is required',
        timestamp: new Date().toISOString()
      });
    }

    const result = await miService.executeCommand(command, params);
    res.json({
      success: true,
      command,
      params,
      result,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ MI command error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

module.exports = router;
