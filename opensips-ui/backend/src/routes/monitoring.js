const express = require('express');
const router = express.Router();
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { pool } = require('../config/database');
const miService = require('../services/miService');

// Permission middleware for monitoring
const requireMonitoringRead = requirePermission('monitoring.read');

// Get system overview
router.get('/overview', authenticateToken, requireMonitoringRead, async (req, res) => {
  try {
    console.log('📊 Getting system overview...');
    
    // Get basic system stats
    const queries = {
      totalUsers: `SELECT COUNT(*) as count FROM subscriber`,
      totalCalls: `SELECT COUNT(*) as count FROM acc WHERE method = 'INVITE' AND time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`,
      activeCalls: `SELECT COUNT(*) as count FROM dialog WHERE state = 4`,
      successfulCalls: `SELECT COUNT(*) as count FROM acc WHERE method = 'INVITE' AND sip_code = '200' AND time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`,
      failedCalls: `SELECT COUNT(*) as count FROM acc WHERE method = 'INVITE' AND sip_code != '200' AND time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`,
      avgCallDuration: `SELECT AVG(duration) as avg_duration FROM acc WHERE method = 'INVITE' AND sip_code = '200' AND duration > 0 AND time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`
    };

    const results = {};
    for (const [key, query] of Object.entries(queries)) {
      try {
        const [result] = await pool.execute(query);
        results[key] = result[0];
      } catch (error) {
        console.error(`❌ Error executing query ${key}:`, error);
        results[key] = { count: 0, avg_duration: 0 };
      }
    }

    // Calculate success rate
    const totalCallsCount = results.totalCalls.count || 0;
    const successfulCallsCount = results.successfulCalls.count || 0;
    const successRate = totalCallsCount > 0 ? ((successfulCallsCount / totalCallsCount) * 100).toFixed(2) : 0;

    // Try to get OpenSIPS statistics via MI
    let opensipsStats = null;
    try {
      const miResult = await miService.getStatistics();
      if (miResult.success) {
        opensipsStats = miResult.data;
      }
    } catch (error) {
      console.log('⚠️ Could not get OpenSIPS MI stats:', error.message);
    }

    const overview = {
      users: {
        total: results.totalUsers.count,
        online: 0 // TODO: Implement online users tracking
      },
      calls: {
        total24h: totalCallsCount,
        active: results.activeCalls.count,
        successful: successfulCallsCount,
        failed: results.failedCalls.count,
        successRate: parseFloat(successRate),
        avgDuration: Math.round(results.avgCallDuration.avg_duration || 0)
      },
      system: {
        uptime: opensipsStats?.uptime || 'N/A',
        version: opensipsStats?.version || 'N/A',
        memory: opensipsStats?.memory || 'N/A'
      }
    };

    console.log('📊 System overview:', overview);

    res.json({
      success: true,
      data: overview
    });

  } catch (error) {
    console.error('❌ Error getting system overview:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get system overview',
      error: error.message
    });
  }
});

// Get real-time metrics
router.get('/metrics', authenticateToken, requireMonitoringRead, async (req, res) => {
  try {
    console.log('📈 Getting real-time metrics...');
    
    const { period = '1h' } = req.query;
    
    // Calculate time range
    let timeCondition = '';
    switch (period) {
      case '1h':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)`;
        break;
      case '6h':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 6 HOUR)`;
        break;
      case '24h':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`;
        break;
      case '7d':
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 7 DAY)`;
        break;
      default:
        timeCondition = `time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)`;
    }

    // Get call metrics over time
    const callMetricsQuery = `
      SELECT 
        DATE_FORMAT(time, '%Y-%m-%d %H:00:00') as hour,
        COUNT(*) as total_calls,
        SUM(CASE WHEN sip_code = '200' THEN 1 ELSE 0 END) as successful_calls,
        SUM(CASE WHEN sip_code != '200' THEN 1 ELSE 0 END) as failed_calls,
        AVG(CASE WHEN sip_code = '200' AND duration > 0 THEN duration ELSE NULL END) as avg_duration
      FROM acc 
      WHERE method = 'INVITE' AND ${timeCondition}
      GROUP BY DATE_FORMAT(time, '%Y-%m-%d %H:00:00')
      ORDER BY hour ASC
    `;

    const [callMetrics] = await pool.execute(callMetricsQuery);

    // Get top callers
    const topCallersQuery = `
      SELECT 
        from_tag as caller,
        COUNT(*) as call_count,
        SUM(CASE WHEN sip_code = '200' THEN 1 ELSE 0 END) as successful_calls,
        AVG(CASE WHEN sip_code = '200' AND duration > 0 THEN duration ELSE NULL END) as avg_duration
      FROM acc 
      WHERE method = 'INVITE' AND ${timeCondition}
      GROUP BY from_tag
      ORDER BY call_count DESC
      LIMIT 10
    `;

    const [topCallers] = await pool.execute(topCallersQuery);

    // Get error distribution
    const errorDistributionQuery = `
      SELECT 
        sip_code,
        sip_reason,
        COUNT(*) as count
      FROM acc 
      WHERE method = 'INVITE' AND sip_code != '200' AND ${timeCondition}
      GROUP BY sip_code, sip_reason
      ORDER BY count DESC
      LIMIT 10
    `;

    const [errorDistribution] = await pool.execute(errorDistributionQuery);

    // Try to get OpenSIPS real-time stats
    let opensipsMetrics = null;
    try {
      const miResult = await miService.getStatistics();
      if (miResult.success) {
        opensipsMetrics = miResult.data;
      }
    } catch (error) {
      console.log('⚠️ Could not get OpenSIPS MI metrics:', error.message);
    }

    const metrics = {
      period,
      callMetrics: callMetrics.map(metric => ({
        time: metric.hour,
        totalCalls: metric.total_calls,
        successfulCalls: metric.successful_calls,
        failedCalls: metric.failed_calls,
        avgDuration: Math.round(metric.avg_duration || 0),
        successRate: metric.total_calls > 0 ? ((metric.successful_calls / metric.total_calls) * 100).toFixed(2) : 0
      })),
      topCallers: topCallers.map(caller => ({
        caller: caller.caller,
        callCount: caller.call_count,
        successfulCalls: caller.successful_calls,
        avgDuration: Math.round(caller.avg_duration || 0),
        successRate: caller.call_count > 0 ? ((caller.successful_calls / caller.call_count) * 100).toFixed(2) : 0
      })),
      errorDistribution: errorDistribution.map(error => ({
        code: error.sip_code,
        reason: error.sip_reason,
        count: error.count
      })),
      opensipsMetrics
    };

    console.log('📈 Real-time metrics collected');

    res.json({
      success: true,
      data: metrics
    });

  } catch (error) {
    console.error('❌ Error getting metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get metrics',
      error: error.message
    });
  }
});

// Get system health
router.get('/health', authenticateToken, requireMonitoringRead, async (req, res) => {
  try {
    console.log('🏥 Checking system health...');
    
    const health = {
      database: 'unknown',
      opensips: 'unknown',
      timestamp: new Date().toISOString()
    };

    // Check database health
    try {
      await pool.execute('SELECT 1');
      health.database = 'healthy';
    } catch (error) {
      health.database = 'unhealthy';
      health.databaseError = error.message;
    }

    // Check OpenSIPS health via MI
    try {
      const miResult = await miService.ping();
      health.opensips = miResult ? 'healthy' : 'unhealthy';
    } catch (error) {
      health.opensips = 'unhealthy';
      health.opensipsError = error.message;
    }

    // Overall health status
    health.overall = (health.database === 'healthy' && health.opensips === 'healthy') ? 'healthy' : 'degraded';

    console.log('🏥 System health check completed:', health);

    res.json({
      success: true,
      data: health
    });

  } catch (error) {
    console.error('❌ Error checking system health:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check system health',
      error: error.message
    });
  }
});

// Get OpenSIPS statistics
router.get('/opensips-stats', authenticateToken, requireMonitoringRead, async (req, res) => {
  try {
    console.log('🔧 Getting OpenSIPS statistics...');
    
    const result = await miService.getStatistics();
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(503).json({
        success: false,
        message: 'OpenSIPS MI interface not available',
        error: result.error
      });
    }

  } catch (error) {
    console.error('❌ Error getting OpenSIPS stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get OpenSIPS statistics',
      error: error.message
    });
  }
});

module.exports = router;
