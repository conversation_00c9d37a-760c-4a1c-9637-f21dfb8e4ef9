const express = require('express');
const { executeQuery } = require('../config/database');

const router = express.Router();

// Standard response format
const sendResponse = (res, success, data = null, error = null, statusCode = 200) => {
  const response = {
    success,
    timestamp: new Date().toISOString()
  };
  
  if (success) {
    response.data = data;
  } else {
    response.error = error;
  }
  
  res.status(statusCode).json(response);
};

// GET /api/stats - System statistics (optimized single endpoint)
router.get('/stats', async (req, res) => {
  try {
    console.log('📊 Public stats endpoint called');

    const queries = [
      'SELECT COUNT(*) as active_registrations FROM location WHERE expires > UNIX_TIMESTAMP()',
      'SELECT COUNT(*) as active_dialogs FROM dialog WHERE state = 4',
      'SELECT COUNT(*) as calls_today FROM acc WHERE DATE(FROM_UNIXTIME(time)) = CURDATE() AND method = "INVITE"',
      'SELECT COUNT(*) as calls_last_hour FROM acc WHERE time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR)) AND method = "INVITE"',
      'SELECT COUNT(*) as total_users FROM subscriber',
      'SELECT COUNT(*) as failed_calls_today FROM acc WHERE DATE(FROM_UNIXTIME(time)) = CURDATE() AND method = "INVITE" AND sip_code >= 400',
      'SELECT AVG(duration) as avg_duration FROM acc WHERE DATE(FROM_UNIXTIME(time)) = CURDATE() AND method = "BYE" AND duration > 0',
      'SELECT COUNT(*) as calls_this_month FROM acc WHERE YEAR(FROM_UNIXTIME(time)) = YEAR(CURDATE()) AND MONTH(FROM_UNIXTIME(time)) = MONTH(CURDATE()) AND method = "INVITE"'
    ];

    const results = await Promise.all(
      queries.map(query => executeQuery(query).catch(err => {
        console.error(`Query failed: ${query}`, err);
        return { success: false, data: [{ [query.split(' ')[1].toLowerCase()]: 0 }] };
      }))
    );

    const callsToday = results[2].success ? results[2].data[0].calls_today : 0;
    const failedCallsToday = results[5].success ? results[5].data[0].failed_calls_today : 0;
    const avgDuration = results[6].success ? results[6].data[0].avg_duration : 0;

    const stats = {
      activeRegistrations: results[0].success ? results[0].data[0].active_registrations : 0,
      activeDialogs: results[1].success ? results[1].data[0].active_dialogs : 0,
      callsToday: callsToday,
      callsLastHour: results[3].success ? results[3].data[0].calls_last_hour : 0,
      totalUsers: results[4].success ? results[4].data[0].total_users : 0,
      failedCallsToday: failedCallsToday,
      successRate: callsToday > 0 ? parseFloat(((callsToday - failedCallsToday) / callsToday * 100).toFixed(2)) : 100,
      avgCallDuration: avgDuration ? Math.round(avgDuration) : 0,
      callsThisMonth: results[7].success ? results[7].data[0].calls_this_month : 0
    };

    console.log('📊 Public stats:', stats);
    sendResponse(res, true, stats);

  } catch (error) {
    console.error('❌ Public stats error:', error);
    sendResponse(res, false, null, 'Internal server error', 500);
  }
});

// GET /api/public/users - User list with status
router.get('/users', async (req, res) => {
  try {
    console.log('👥 Public users endpoint called from:', req.ip);
    console.log('👥 Request headers:', req.headers['user-agent']);
    
    const result = await executeQuery(`
      SELECT
        s.username,
        s.domain,
        s.email_address,
        s.ha1 as password_hash,
        l.contact,
        l.expires,
        CASE
          WHEN l.expires > UNIX_TIMESTAMP() THEN 'online'
          ELSE 'offline'
        END as status,
        l.user_agent,
        l.last_modified as last_seen,
        l.received,
        l.path,
        l.socket,
        l.methods,
        l.cseq,
        l.q as priority,
        CASE
          WHEN l.expires > UNIX_TIMESTAMP() THEN TIMESTAMPDIFF(SECOND, NOW(), FROM_UNIXTIME(l.expires))
          ELSE 0
        END as expires_in_seconds,
        FROM_UNIXTIME(l.last_modified) as last_seen_formatted,
        INET_NTOA(l.received & 0xFFFFFFFF) as ip_address
      FROM subscriber s
      LEFT JOIN location l ON s.username = l.username AND s.domain = l.domain
      ORDER BY
        CASE WHEN l.expires > UNIX_TIMESTAMP() THEN 0 ELSE 1 END,
        s.username
    `);

    if (result.success) {
      const users = result.data;
      console.log('👥 Public users:', users.length, 'total');
      
      const responseData = {
        users: users,
        total: users.length,
        online: users.filter(u => u.status === 'online').length,
        offline: users.filter(u => u.status === 'offline').length
      };
      
      sendResponse(res, true, responseData);
    } else {
      sendResponse(res, false, null, 'Failed to fetch users', 500);
    }
  } catch (error) {
    console.error('❌ Public users error:', error);
    sendResponse(res, false, null, 'Internal server error', 500);
  }
});

// GET /api/public/users/stats - Detailed user statistics
router.get('/users/stats', async (req, res) => {
  try {
    console.log('📊 Public users stats endpoint called');

    // Get user registration stats
    const registrationStats = await executeQuery(`
      SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN l.expires > UNIX_TIMESTAMP() THEN 1 END) as online_users,
        COUNT(CASE WHEN l.expires <= UNIX_TIMESTAMP() OR l.expires IS NULL THEN 1 END) as offline_users,
        AVG(CASE WHEN l.expires > UNIX_TIMESTAMP() THEN TIMESTAMPDIFF(SECOND, NOW(), FROM_UNIXTIME(l.expires)) END) as avg_expires_in,
        COUNT(DISTINCT l.user_agent) as unique_user_agents,
        COUNT(DISTINCT INET_NTOA(l.received & 0xFFFFFFFF)) as unique_ip_addresses
      FROM subscriber s
      LEFT JOIN location l ON s.username = l.username AND s.domain = l.domain
    `);

    // Get user agent distribution
    const userAgentStats = await executeQuery(`
      SELECT
        l.user_agent,
        COUNT(*) as count,
        COUNT(CASE WHEN l.expires > UNIX_TIMESTAMP() THEN 1 END) as online_count
      FROM location l
      WHERE l.user_agent IS NOT NULL AND l.user_agent != ''
      GROUP BY l.user_agent
      ORDER BY count DESC
      LIMIT 10
    `);

    // Get domain distribution
    const domainStats = await executeQuery(`
      SELECT
        s.domain,
        COUNT(*) as total_users,
        COUNT(CASE WHEN l.expires > UNIX_TIMESTAMP() THEN 1 END) as online_users
      FROM subscriber s
      LEFT JOIN location l ON s.username = l.username AND s.domain = l.domain
      GROUP BY s.domain
      ORDER BY total_users DESC
    `);

    const stats = registrationStats.success ? registrationStats.data[0] : {};
    const userAgents = userAgentStats.success ? userAgentStats.data : [];
    const domains = domainStats.success ? domainStats.data : [];

    console.log('📊 Users stats:', stats);

    const responseData = {
      overview: stats,
      userAgents: userAgents,
      domains: domains,
      timestamp: new Date().toISOString()
    };

    sendResponse(res, true, responseData);

  } catch (error) {
    console.error('❌ Public users stats error:', error);
    sendResponse(res, false, null, 'Internal server error', 500);
  }
});

// GET /api/calls - Active and recent calls (optimized single endpoint)
router.get('/calls', async (req, res) => {
  try {
    console.log('📞 Public calls endpoint called');
    
    const activeCallsResult = await executeQuery(`
      SELECT 
        dlg_id,
        callid,
        from_uri,
        to_uri,
        state,
        start_time,
        TIMESTAMPDIFF(SECOND, FROM_UNIXTIME(start_time), NOW()) as duration
      FROM dialog 
      WHERE state = 4
      ORDER BY start_time DESC
    `);

    const recentCallsResult = await executeQuery(`
      SELECT 
        method,
        from_tag as caller,
        to_tag as callee,
        sip_code,
        sip_reason,
        duration,
        time as timestamp
      FROM acc 
      WHERE method IN ('INVITE', 'BYE')
      ORDER BY time DESC 
      LIMIT 50
    `);

    const activeCalls = activeCallsResult.success ? activeCallsResult.data : [];
    const recentCalls = recentCallsResult.success ? recentCallsResult.data : [];

    console.log('📞 Public calls:', activeCalls.length, 'active,', recentCalls.length, 'recent');

    const responseData = {
      activeCalls: activeCalls,
      recentCalls: recentCalls,
      totalActive: activeCalls.length,
      totalRecent: recentCalls.length
    };

    sendResponse(res, true, responseData);

  } catch (error) {
    console.error('❌ Public calls error:', error);
    sendResponse(res, false, null, 'Internal server error', 500);
  }
});

// GET /api/cdr - Call Detail Records (optimized single endpoint)
router.get('/cdr', async (req, res) => {
  try {
    console.log('📋 Public CDR endpoint called');
    
    const { limit = 100, date, caller, callee } = req.query;
    
    let whereClause = "WHERE method = 'INVITE'";
    const params = [];
    
    if (date) {
      whereClause += " AND DATE(FROM_UNIXTIME(time)) = ?";
      params.push(date);
    }
    
    if (caller) {
      whereClause += " AND from_tag LIKE ?";
      params.push(`%${caller}%`);
    }
    
    if (callee) {
      whereClause += " AND to_tag LIKE ?";
      params.push(`%${callee}%`);
    }

    const query = `
      SELECT 
        callid,
        method,
        from_tag as caller,
        to_tag as callee,
        sip_code,
        sip_reason,
        duration,
        time as timestamp
      FROM acc 
      ${whereClause}
      ORDER BY time DESC 
      LIMIT ${parseInt(limit)}
    `;
    
    console.log('📋 CDR Query:', query);
    
    const result = await executeQuery(query, params);

    if (result.success) {
      console.log('📋 Public CDR:', result.data.length, 'records');
      
      const responseData = {
        records: result.data,
        total: result.data.length,
        filters: { date, caller, callee, limit }
      };
      
      sendResponse(res, true, responseData);
    } else {
      console.error('📋 CDR Query failed:', result.error);
      sendResponse(res, false, null, 'Failed to fetch CDR', 500);
    }
  } catch (error) {
    console.error('❌ Public CDR error:', error);
    sendResponse(res, false, null, 'Internal server error', 500);
  }
});

// GET /api/health - Health check (optimized single endpoint)
router.get('/health', (req, res) => {
  sendResponse(res, true, {
    status: 'OK',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version
  });
});

module.exports = router;
