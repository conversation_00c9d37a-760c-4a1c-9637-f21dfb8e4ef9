const express = require('express');
const router = express.Router();
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { pool } = require('../config/database');
const fs = require('fs').promises;
const path = require('path');

// Permission middleware for recordings
const requireRecordingsRead = requirePermission('recordings.read');
const requireRecordingsWrite = requirePermission('recordings.write');

// Get call recordings
router.get('/', authenticateToken, requireRecordingsRead, async (req, res) => {
  try {
    console.log('🎵 Getting call recordings...');
    
    const { 
      page = 1, 
      limit = 20, 
      caller, 
      callee, 
      from_date, 
      to_date,
      duration_min,
      duration_max
    } = req.query;

    const offset = (page - 1) * limit;
    
    // Build WHERE conditions
    let whereConditions = [];
    let queryParams = [];

    if (caller) {
      whereConditions.push('caller LIKE ?');
      queryParams.push(`%${caller}%`);
    }

    if (callee) {
      whereConditions.push('callee LIKE ?');
      queryParams.push(`%${callee}%`);
    }

    if (from_date) {
      whereConditions.push('call_date >= ?');
      queryParams.push(from_date);
    }

    if (to_date) {
      whereConditions.push('call_date <= ?');
      queryParams.push(to_date);
    }

    if (duration_min) {
      whereConditions.push('duration >= ?');
      queryParams.push(parseInt(duration_min));
    }

    if (duration_max) {
      whereConditions.push('duration <= ?');
      queryParams.push(parseInt(duration_max));
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // Create recordings table if it doesn't exist (mock data)
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS call_recordings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        callid VARCHAR(255) NOT NULL,
        caller VARCHAR(100) NOT NULL,
        callee VARCHAR(100) NOT NULL,
        call_date DATETIME NOT NULL,
        duration INT NOT NULL,
        file_path VARCHAR(500),
        file_size BIGINT,
        format VARCHAR(10) DEFAULT 'wav',
        quality VARCHAR(20) DEFAULT 'standard',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_callid (callid),
        INDEX idx_call_date (call_date),
        INDEX idx_caller (caller),
        INDEX idx_callee (callee)
      )
    `);

    // Insert some mock recordings if table is empty
    const [existingRecordings] = await pool.execute('SELECT COUNT(*) as count FROM call_recordings');
    if (existingRecordings[0].count === 0) {
      await pool.execute(`
        INSERT INTO call_recordings (callid, caller, callee, call_date, duration, file_path, file_size, format, quality) VALUES
        ('rec-001-demo', '<EMAIL>', '<EMAIL>', NOW() - INTERVAL 2 HOUR, 120, '/recordings/2024/01/rec-001-demo.wav', 1920000, 'wav', 'high'),
        ('rec-002-demo', '<EMAIL>', '<EMAIL>', NOW() - INTERVAL 1 HOUR, 85, '/recordings/2024/01/rec-002-demo.wav', 1360000, 'wav', 'standard'),
        ('rec-003-demo', '<EMAIL>', '<EMAIL>', NOW() - INTERVAL 30 MINUTE, 45, '/recordings/2024/01/rec-003-demo.wav', 720000, 'wav', 'standard'),
        ('rec-004-demo', '<EMAIL>', '<EMAIL>', NOW() - INTERVAL 15 MINUTE, 180, '/recordings/2024/01/rec-004-demo.wav', 2880000, 'wav', 'high'),
        ('rec-005-demo', '<EMAIL>', '<EMAIL>', NOW() - INTERVAL 5 MINUTE, 95, '/recordings/2024/01/rec-005-demo.wav', 1520000, 'wav', 'standard')
      `);
    }

    // Get recordings with pagination
    const recordingsQuery = `
      SELECT
        id,
        callid,
        caller,
        callee,
        call_date,
        duration,
        file_path,
        file_size,
        format,
        quality,
        created_at
      FROM call_recordings
      ${whereClause}
      ORDER BY call_date DESC
      LIMIT ? OFFSET ?
    `;

    const [recordings] = await pool.execute(recordingsQuery, [...queryParams, parseInt(limit), offset]);

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM call_recordings ${whereClause}`;
    const [countResult] = await pool.execute(countQuery, queryParams);
    const total = countResult[0].total;

    // Format recordings
    const formattedRecordings = recordings.map(recording => ({
      id: recording.id,
      callId: recording.callid,
      caller: recording.caller,
      callee: recording.callee,
      callDate: recording.call_date,
      duration: recording.duration,
      filePath: recording.file_path,
      fileSize: recording.file_size,
      format: recording.format,
      quality: recording.quality,
      downloadUrl: `/api/recordings/download/${recording.id}`,
      streamUrl: `/api/recordings/stream/${recording.id}`,
      createdAt: recording.created_at
    }));

    console.log(`🎵 Found ${recordings.length} recordings`);

    res.json({
      success: true,
      data: {
        recordings: formattedRecordings,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('❌ Error getting recordings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recordings',
      error: error.message
    });
  }
});

// Get recording statistics
router.get('/stats', authenticateToken, requireRecordingsRead, async (req, res) => {
  try {
    console.log('🎵 Getting recording statistics...');
    
    const { period = '24h' } = req.query;
    
    // Calculate time range
    let timeCondition = '';
    switch (period) {
      case '1h':
        timeCondition = `call_date >= DATE_SUB(NOW(), INTERVAL 1 HOUR)`;
        break;
      case '24h':
        timeCondition = `call_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`;
        break;
      case '7d':
        timeCondition = `call_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)`;
        break;
      case '30d':
        timeCondition = `call_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)`;
        break;
      default:
        timeCondition = `call_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR)`;
    }

    const queries = {
      totalRecordings: `SELECT COUNT(*) as count FROM call_recordings WHERE ${timeCondition}`,
      totalDuration: `SELECT SUM(duration) as total FROM call_recordings WHERE ${timeCondition}`,
      totalSize: `SELECT SUM(file_size) as total FROM call_recordings WHERE ${timeCondition}`,
      avgDuration: `SELECT AVG(duration) as avg FROM call_recordings WHERE ${timeCondition}`,
      qualityDistribution: `
        SELECT quality, COUNT(*) as count 
        FROM call_recordings 
        WHERE ${timeCondition}
        GROUP BY quality
      `,
      formatDistribution: `
        SELECT format, COUNT(*) as count 
        FROM call_recordings 
        WHERE ${timeCondition}
        GROUP BY format
      `
    };

    const results = {};
    for (const [key, query] of Object.entries(queries)) {
      try {
        const [result] = await pool.execute(query);
        results[key] = result;
      } catch (error) {
        console.error(`❌ Error executing query ${key}:`, error);
        results[key] = key.includes('Distribution') ? [] : [{ count: 0, total: 0, avg: 0 }];
      }
    }

    const stats = {
      period,
      totalRecordings: results.totalRecordings[0].count || 0,
      totalDuration: results.totalDuration[0].total || 0,
      totalSize: results.totalSize[0].total || 0,
      avgDuration: Math.round(results.avgDuration[0].avg || 0),
      qualityDistribution: results.qualityDistribution,
      formatDistribution: results.formatDistribution
    };

    console.log('🎵 Recording statistics collected');

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('❌ Error getting recording stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recording statistics',
      error: error.message
    });
  }
});

// Download recording
router.get('/download/:id', authenticateToken, requireRecordingsRead, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🎵 Downloading recording ${id}...`);
    
    // Get recording info
    const [recordings] = await pool.execute(
      'SELECT * FROM call_recordings WHERE id = ?',
      [id]
    );

    if (recordings.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Recording not found'
      });
    }

    const recording = recordings[0];
    
    // For demo purposes, return a mock response
    res.json({
      success: true,
      message: 'Recording download would start here',
      data: {
        id: recording.id,
        filename: `recording-${recording.callid}.${recording.format}`,
        size: recording.file_size,
        downloadUrl: `${req.protocol}://${req.get('host')}/recordings/${recording.callid}.${recording.format}`
      }
    });

  } catch (error) {
    console.error('❌ Error downloading recording:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download recording',
      error: error.message
    });
  }
});

// Stream recording
router.get('/stream/:id', authenticateToken, requireRecordingsRead, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🎵 Streaming recording ${id}...`);
    
    // Get recording info
    const [recordings] = await pool.execute(
      'SELECT * FROM call_recordings WHERE id = ?',
      [id]
    );

    if (recordings.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Recording not found'
      });
    }

    const recording = recordings[0];
    
    // For demo purposes, return a mock response
    res.json({
      success: true,
      message: 'Recording stream would start here',
      data: {
        id: recording.id,
        streamUrl: `${req.protocol}://${req.get('host')}/stream/${recording.callid}.${recording.format}`,
        duration: recording.duration,
        format: recording.format
      }
    });

  } catch (error) {
    console.error('❌ Error streaming recording:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stream recording',
      error: error.message
    });
  }
});

// Delete recording
router.delete('/:id', authenticateToken, requireRecordingsWrite, async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`🎵 Deleting recording ${id}...`);
    
    // Get recording info first
    const [recordings] = await pool.execute(
      'SELECT * FROM call_recordings WHERE id = ?',
      [id]
    );

    if (recordings.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Recording not found'
      });
    }

    // Delete from database
    await pool.execute('DELETE FROM call_recordings WHERE id = ?', [id]);
    
    console.log(`🎵 Recording ${id} deleted successfully`);

    res.json({
      success: true,
      message: 'Recording deleted successfully'
    });

  } catch (error) {
    console.error('❌ Error deleting recording:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete recording',
      error: error.message
    });
  }
});

module.exports = router;
