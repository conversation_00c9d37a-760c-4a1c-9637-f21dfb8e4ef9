const express = require('express');
const router = express.Router();
const { authenticateToken, requirePermission } = require('../middleware/auth');
const { pool } = require('../config/database');

// Permission middleware for settings
const requireSettingsRead = requirePermission('settings.read');
const requireSettingsWrite = requirePermission('settings.write');

// Get all system settings
router.get('/', authenticateToken, requireSettingsRead, async (req, res) => {
  try {
    console.log('⚙️ Getting system settings...');
    
    // Get settings from database or use defaults
    const [settings] = await pool.execute(`
      SELECT setting_key, setting_value, description, category 
      FROM system_settings 
      ORDER BY category, setting_key
    `).catch(() => [[]]);

    // Default settings if table doesn't exist
    const defaultSettings = {
      general: {
        system_name: {
          value: 'OpenSIPS PBX',
          description: 'System display name',
          type: 'text'
        },
        timezone: {
          value: 'Asia/Ho_Chi_Minh',
          description: 'System timezone',
          type: 'select',
          options: ['Asia/Ho_Chi_Minh', 'UTC', 'America/New_York', 'Europe/London']
        },
        language: {
          value: 'vi',
          description: 'Default language',
          type: 'select',
          options: ['vi', 'en', 'zh']
        },
        session_timeout: {
          value: '3600',
          description: 'Session timeout in seconds',
          type: 'number'
        }
      },
      sip: {
        domain: {
          value: 'localhost',
          description: 'SIP domain',
          type: 'text'
        },
        port: {
          value: '5060',
          description: 'SIP port',
          type: 'number'
        },
        transport: {
          value: 'UDP',
          description: 'SIP transport protocol',
          type: 'select',
          options: ['UDP', 'TCP', 'TLS', 'WS', 'WSS']
        },
        registration_timeout: {
          value: '3600',
          description: 'Registration timeout in seconds',
          type: 'number'
        }
      },
      security: {
        password_policy: {
          value: 'medium',
          description: 'Password complexity policy',
          type: 'select',
          options: ['low', 'medium', 'high']
        },
        max_login_attempts: {
          value: '5',
          description: 'Maximum login attempts before lockout',
          type: 'number'
        },
        lockout_duration: {
          value: '300',
          description: 'Account lockout duration in seconds',
          type: 'number'
        },
        enable_2fa: {
          value: 'false',
          description: 'Enable two-factor authentication',
          type: 'boolean'
        }
      },
      notifications: {
        email_enabled: {
          value: 'false',
          description: 'Enable email notifications',
          type: 'boolean'
        },
        smtp_server: {
          value: '',
          description: 'SMTP server address',
          type: 'text'
        },
        smtp_port: {
          value: '587',
          description: 'SMTP server port',
          type: 'number'
        },
        admin_email: {
          value: 'admin@localhost',
          description: 'Administrator email address',
          type: 'email'
        }
      },
      monitoring: {
        enable_logging: {
          value: 'true',
          description: 'Enable system logging',
          type: 'boolean'
        },
        log_level: {
          value: 'info',
          description: 'System log level',
          type: 'select',
          options: ['debug', 'info', 'warn', 'error']
        },
        retention_days: {
          value: '30',
          description: 'Log retention period in days',
          type: 'number'
        },
        enable_metrics: {
          value: 'true',
          description: 'Enable performance metrics collection',
          type: 'boolean'
        }
      }
    };

    // Convert database settings to structured format
    const structuredSettings = { ...defaultSettings };
    
    settings.forEach(setting => {
      const category = setting.category || 'general';
      if (!structuredSettings[category]) {
        structuredSettings[category] = {};
      }
      
      structuredSettings[category][setting.setting_key] = {
        value: setting.setting_value,
        description: setting.description || '',
        type: 'text' // Default type
      };
    });

    console.log('⚙️ Settings retrieved successfully');

    res.json({
      success: true,
      data: {
        settings: structuredSettings,
        lastUpdated: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error getting settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get settings',
      error: error.message
    });
  }
});

// Update system settings
router.put('/', authenticateToken, requireSettingsWrite, async (req, res) => {
  try {
    console.log('⚙️ Updating system settings...');
    
    const { settings } = req.body;
    
    if (!settings || typeof settings !== 'object') {
      return res.status(400).json({
        success: false,
        message: 'Invalid settings data'
      });
    }

    // Create settings table if it doesn't exist
    await pool.execute(`
      CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL,
        setting_value TEXT,
        description TEXT,
        category VARCHAR(50) DEFAULT 'general',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_key_category (setting_key, category)
      )
    `);

    // Update settings
    const updatePromises = [];
    
    Object.entries(settings).forEach(([category, categorySettings]) => {
      Object.entries(categorySettings).forEach(([key, settingData]) => {
        const value = typeof settingData === 'object' ? settingData.value : settingData;
        const description = typeof settingData === 'object' ? settingData.description : '';
        
        const updatePromise = pool.execute(`
          INSERT INTO system_settings (setting_key, setting_value, description, category)
          VALUES (?, ?, ?, ?)
          ON DUPLICATE KEY UPDATE 
            setting_value = VALUES(setting_value),
            description = VALUES(description),
            updated_at = CURRENT_TIMESTAMP
        `, [key, value, description, category]);
        
        updatePromises.push(updatePromise);
      });
    });

    await Promise.all(updatePromises);

    console.log('⚙️ Settings updated successfully');

    res.json({
      success: true,
      message: 'Settings updated successfully',
      data: {
        updatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update settings',
      error: error.message
    });
  }
});

// Get specific setting category
router.get('/:category', authenticateToken, requireSettingsRead, async (req, res) => {
  try {
    const { category } = req.params;
    console.log(`⚙️ Getting settings for category: ${category}`);
    
    const [settings] = await pool.execute(`
      SELECT setting_key, setting_value, description 
      FROM system_settings 
      WHERE category = ?
      ORDER BY setting_key
    `, [category]).catch(() => [[]]);

    const categorySettings = {};
    settings.forEach(setting => {
      categorySettings[setting.setting_key] = {
        value: setting.setting_value,
        description: setting.description || ''
      };
    });

    res.json({
      success: true,
      data: {
        category,
        settings: categorySettings
      }
    });

  } catch (error) {
    console.error('❌ Error getting category settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get category settings',
      error: error.message
    });
  }
});

// Reset settings to defaults
router.post('/reset', authenticateToken, requireSettingsWrite, async (req, res) => {
  try {
    console.log('⚙️ Resetting settings to defaults...');
    
    const { category } = req.body;
    
    if (category) {
      // Reset specific category
      await pool.execute(`
        DELETE FROM system_settings WHERE category = ?
      `, [category]);
      
      console.log(`⚙️ Settings for category '${category}' reset to defaults`);
    } else {
      // Reset all settings
      await pool.execute(`DELETE FROM system_settings`);
      console.log('⚙️ All settings reset to defaults');
    }

    res.json({
      success: true,
      message: category ? 
        `Settings for category '${category}' reset to defaults` : 
        'All settings reset to defaults'
    });

  } catch (error) {
    console.error('❌ Error resetting settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to reset settings',
      error: error.message
    });
  }
});

// Export/Import settings
router.get('/export/backup', authenticateToken, requireSettingsRead, async (req, res) => {
  try {
    console.log('⚙️ Exporting settings backup...');
    
    const [settings] = await pool.execute(`
      SELECT setting_key, setting_value, description, category 
      FROM system_settings 
      ORDER BY category, setting_key
    `).catch(() => [[]]);

    const backup = {
      version: '1.0',
      timestamp: new Date().toISOString(),
      settings: settings
    };

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename=opensips-settings-backup.json');
    res.json(backup);

  } catch (error) {
    console.error('❌ Error exporting settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export settings',
      error: error.message
    });
  }
});

module.exports = router;
