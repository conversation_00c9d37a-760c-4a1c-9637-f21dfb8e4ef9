const express = require('express');
const bcrypt = require('bcryptjs');
const mysql = require('mysql2/promise');
const { authenticateToken } = require('./auth');
const router = express.Router();

// Database connection
const dbConfig = {
  host: process.env.DB_HOST || 'mysql',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'opensips',
  password: process.env.DB_PASSWORD || 'opensips_password',
  database: process.env.DB_NAME || 'opensips',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

const pool = mysql.createPool(dbConfig);

// Test endpoint
router.get('/test', (req, res) => {
  console.log('🧪 Test endpoint called');
  res.json({ success: true, message: 'Users router is working' });
});

// Middleware to check permissions
const requirePermission = (permission) => {
  return (req, res, next) => {
    console.log('🔐 Checking permission:', permission);
    console.log('🔐 User permissions:', req.user.permissions);

    if (!req.user.permissions.includes(permission)) {
      console.log('❌ Permission denied for:', permission);
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }
    console.log('✅ Permission granted for:', permission);
    next();
  };
};

// Get all SIP users
router.get('/sip', authenticateToken, requirePermission('users.read'), async (req, res) => {
  console.log('🔍 GET /api/users/sip called');
  try {
    const { page = 1, limit = 50, search = '', domain = '', status = '' } = req.query;
    const offset = (page - 1) * limit;

    // Simple query first to test
    const query = `
      SELECT
        s.id,
        s.username,
        s.domain,
        s.email_address,
        'offline' as status
      FROM subscriber s
      ORDER BY s.username, s.domain
      LIMIT ? OFFSET ?
    `;

    console.log('🔍 Executing query:', query);
    console.log('🔍 Query params:', [parseInt(limit), parseInt(offset)]);

    const [users] = await pool.execute(query, [parseInt(limit), parseInt(offset)]);
    console.log('🔍 Users found:', users.length);

    // Get total count
    const [countResult] = await pool.execute('SELECT COUNT(*) as total FROM subscriber');
    const total = countResult[0].total;
    console.log('🔍 Total users:', total);



    // Process users data
    const processedUsers = users.map(user => ({
      id: user.id,
      username: user.username,
      domain: user.domain,
      email_address: user.email_address,
      status: user.status,
      isRegistered: user.status === 'online'
    }));

    res.json({
      success: true,
      data: {
        users: processedUsers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('❌ Get SIP users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch users'
    });
  }
});

// Get single SIP user
router.get('/sip/:username/:domain', authenticateToken, requirePermission('users.read'), async (req, res) => {
  try {
    const { username, domain } = req.params;

    const query = `
      SELECT
        s.id,
        s.username,
        s.domain,
        s.password,
        s.email_address,
        s.ha1,
        s.ha1b,
        s.rpid,
        l.contact,
        l.expires,
        l.q,
        l.callid,
        l.cseq,
        l.last_modified,
        l.flags,
        l.cflags,
        l.user_agent,
        l.received,
        l.path,
        l.socket,
        CASE
          WHEN l.expires > UNIX_TIMESTAMP() THEN 'online'
          ELSE 'offline'
        END as status
      FROM subscriber s
      LEFT JOIN location l ON s.username = l.username AND s.domain = l.domain
      WHERE s.username = ? AND s.domain = ?
    `;

    const [users] = await pool.execute(query, [username, domain]);

    if (users.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = users[0];
    const processedUser = {
      id: user.id,
      username: user.username,
      domain: user.domain,
      email_address: user.email_address,
      contact: user.contact,
      expires: user.expires,
      lastModified: user.last_modified,
      userAgent: user.user_agent,
      ipAddress: user.received ? user.received.split(':')[0] : null,
      status: user.status,
      isRegistered: user.status === 'online',
      registrationDetails: {
        callId: user.callid,
        cseq: user.cseq,
        q: user.q,
        flags: user.flags,
        cflags: user.cflags,
        path: user.path,
        socket: user.socket
      }
    };

    res.json({
      success: true,
      data: { user: processedUser }
    });

  } catch (error) {
    console.error('Get SIP user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user'
    });
  }
});

// Create new SIP user
router.post('/sip', authenticateToken, requirePermission('users.write'), async (req, res) => {
  try {
    const { username, domain, password } = req.body;

    if (!username || !domain || !password) {
      return res.status(400).json({
        success: false,
        message: 'Username, domain, and password are required'
      });
    }

    // Check if user already exists
    const [existingUsers] = await pool.execute(
      'SELECT id FROM subscriber WHERE username = ? AND domain = ?',
      [username, domain]
    );

    if (existingUsers.length > 0) {
      return res.status(409).json({
        success: false,
        message: 'User already exists'
      });
    }

    // Generate HA1 hashes for digest authentication
    const ha1 = require('crypto')
      .createHash('md5')
      .update(`${username}:${domain}:${password}`)
      .digest('hex');

    const ha1b = require('crypto')
      .createHash('md5')
      .update(`${username}@${domain}:${domain}:${password}`)
      .digest('hex');

    // Insert new user
    const [result] = await pool.execute(`
      INSERT INTO subscriber (username, domain, password, ha1, ha1b) 
      VALUES (?, ?, ?, ?, ?)
    `, [username, domain, password, ha1, ha1b]);

    res.status(201).json({
      success: true,
      message: 'User created successfully',
      data: {
        user: {
          id: result.insertId,
          username,
          domain,
          status: 'offline',
          isRegistered: false
        }
      }
    });

  } catch (error) {
    console.error('Create SIP user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create user'
    });
  }
});

// Update SIP user
router.put('/sip/:username/:domain', authenticateToken, requirePermission('users.write'), async (req, res) => {
  try {
    const { username, domain } = req.params;
    const { password, newUsername, newDomain } = req.body;

    // Check if user exists
    const [existingUsers] = await pool.execute(
      'SELECT id FROM subscriber WHERE username = ? AND domain = ?',
      [username, domain]
    );

    if (existingUsers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const userId = existingUsers[0].id;
    const updateFields = [];
    const updateValues = [];

    // Update username if provided
    if (newUsername && newUsername !== username) {
      updateFields.push('username = ?');
      updateValues.push(newUsername);
    }

    // Update domain if provided
    if (newDomain && newDomain !== domain) {
      updateFields.push('domain = ?');
      updateValues.push(newDomain);
    }

    // Update password if provided
    if (password) {
      const finalUsername = newUsername || username;
      const finalDomain = newDomain || domain;

      const ha1 = require('crypto')
        .createHash('md5')
        .update(`${finalUsername}:${finalDomain}:${password}`)
        .digest('hex');

      const ha1b = require('crypto')
        .createHash('md5')
        .update(`${finalUsername}@${finalDomain}:${finalDomain}:${password}`)
        .digest('hex');

      updateFields.push('password = ?', 'ha1 = ?', 'ha1b = ?');
      updateValues.push(password, ha1, ha1b);
    }

    if (updateFields.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No fields to update'
      });
    }

    updateValues.push(userId);

    await pool.execute(
      `UPDATE subscriber SET ${updateFields.join(', ')} WHERE id = ?`,
      updateValues
    );

    res.json({
      success: true,
      message: 'User updated successfully'
    });

  } catch (error) {
    console.error('Update SIP user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user'
    });
  }
});

// Delete SIP user
router.delete('/sip/:username/:domain', authenticateToken, requirePermission('users.delete'), async (req, res) => {
  try {
    const { username, domain } = req.params;

    // Check if user exists
    const [existingUsers] = await pool.execute(
      'SELECT id FROM subscriber WHERE username = ? AND domain = ?',
      [username, domain]
    );

    if (existingUsers.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Delete from location table first (foreign key constraint)
    await pool.execute(
      'DELETE FROM location WHERE username = ? AND domain = ?',
      [username, domain]
    );

    // Delete from subscriber table
    await pool.execute(
      'DELETE FROM subscriber WHERE username = ? AND domain = ?',
      [username, domain]
    );

    res.json({
      success: true,
      message: 'User deleted successfully'
    });

  } catch (error) {
    console.error('Delete SIP user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete user'
    });
  }
});

// Bulk operations
router.post('/sip/bulk', authenticateToken, requirePermission('users.write'), async (req, res) => {
  try {
    const { action, users } = req.body;

    if (!action || !users || !Array.isArray(users)) {
      return res.status(400).json({
        success: false,
        message: 'Action and users array are required'
      });
    }

    let results = [];

    switch (action) {
      case 'delete':
        for (const user of users) {
          try {
            await pool.execute(
              'DELETE FROM location WHERE username = ? AND domain = ?',
              [user.username, user.domain]
            );
            await pool.execute(
              'DELETE FROM subscriber WHERE username = ? AND domain = ?',
              [user.username, user.domain]
            );
            results.push({ username: user.username, domain: user.domain, success: true });
          } catch (error) {
            results.push({
              username: user.username,
              domain: user.domain,
              success: false,
              error: error.message
            });
          }
        }
        break;

      case 'create':
        for (const user of users) {
          try {
            const { username, domain, password } = user;

            // Generate HA1 hashes
            const ha1 = require('crypto')
              .createHash('md5')
              .update(`${username}:${domain}:${password}`)
              .digest('hex');

            const ha1b = require('crypto')
              .createHash('md5')
              .update(`${username}@${domain}:${domain}:${password}`)
              .digest('hex');

            await pool.execute(`
              INSERT INTO subscriber (username, domain, password, ha1, ha1b)
              VALUES (?, ?, ?, ?, ?)
            `, [username, domain, password, ha1, ha1b]);

            results.push({ username, domain, success: true });
          } catch (error) {
            results.push({
              username: user.username,
              domain: user.domain,
              success: false,
              error: error.message
            });
          }
        }
        break;

      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action. Supported actions: delete, create'
        });
    }

    res.json({
      success: true,
      message: `Bulk ${action} completed`,
      data: { results }
    });

  } catch (error) {
    console.error('Bulk operation error:', error);
    res.status(500).json({
      success: false,
      message: 'Bulk operation failed'
    });
  }
});

// Get user statistics
router.get('/sip/stats', authenticateToken, requirePermission('users.read'), async (req, res) => {
  try {
    // Get total users
    const [totalResult] = await pool.execute('SELECT COUNT(*) as total FROM subscriber');
    const totalUsers = totalResult[0].total;

    // Get online users
    const [onlineResult] = await pool.execute(
      'SELECT COUNT(*) as online FROM location WHERE expires > UNIX_TIMESTAMP()'
    );
    const onlineUsers = onlineResult[0].online;

    // Get users by domain
    const [domainResult] = await pool.execute(`
      SELECT domain, COUNT(*) as count
      FROM subscriber
      GROUP BY domain
      ORDER BY count DESC
    `);

    // Get recent registrations (last 24 hours)
    const [recentResult] = await pool.execute(`
      SELECT COUNT(*) as recent
      FROM location
      WHERE last_modified > UNIX_TIMESTAMP() - 86400
    `);
    const recentRegistrations = recentResult[0].recent;

    res.json({
      success: true,
      data: {
        totalUsers,
        onlineUsers,
        offlineUsers: totalUsers - onlineUsers,
        recentRegistrations,
        domainDistribution: domainResult
      }
    });

  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user statistics'
    });
  }
});

// Force user re-registration (kick user)
router.post('/sip/:username/:domain/kick', authenticateToken, requirePermission('users.write'), async (req, res) => {
  try {
    const { username, domain } = req.params;

    // Remove from location table to force re-registration
    const [result] = await pool.execute(
      'DELETE FROM location WHERE username = ? AND domain = ?',
      [username, domain]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not currently registered'
      });
    }

    res.json({
      success: true,
      message: 'User kicked successfully. They will need to re-register.'
    });

  } catch (error) {
    console.error('Kick user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to kick user'
    });
  }
});

module.exports = router;
