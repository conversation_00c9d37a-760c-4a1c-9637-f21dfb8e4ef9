const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const http = require('http');
const socketIo = require('socket.io');
const RealtimeService = require('./services/realtime');
const EventListenerService = require('./services/eventListener');
require('dotenv').config();

const app = express();
const server = http.createServer(app);
const corsOrigins = [
  "http://localhost:3000",
  "http://localhost:3002",
  "http://localhost:3001", // Add backend URL for testing
  process.env.CORS_ORIGIN
].filter(Boolean);

console.log('🔗 CORS Origins configured:', corsOrigins);

const io = socketIo(server, {
  cors: {
    origin: corsOrigins,
    methods: ["GET", "POST", "PUT", "DELETE"],
    credentials: true,
    allowedHeaders: ["Content-Type", "Authorization"]
  },
  transports: ['websocket', 'polling'], // Prefer websocket first
  allowEIO3: true,
  pingTimeout: 60000,
  pingInterval: 25000,
  upgradeTimeout: 10000,
  maxHttpBufferSize: 1e6,
  allowUpgrades: true,
  compression: true,
  httpCompression: {
    threshold: 1024,
  }
});

console.log('🔌 Socket.IO server initialized with CORS origins:', corsOrigins);

console.log('🔌 Socket.IO server initialized with CORS origin:', process.env.CORS_ORIGIN || "http://localhost:3000");

// Middleware
app.use(helmet());
app.use(cors({
  origin: corsOrigins,
  credentials: true
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Authentication routes
const { router: authRouter } = require('./routes/auth');
app.use('/api/auth', authRouter);

// User management routes
app.use('/api/users', require('./routes/users'));

// Call management routes
app.use('/api/calls', require('./routes/calls'));

// Optimized Routes - Consolidated for better performance
app.use('/api', require('./routes/public'));  // Main API routes (public access)
app.use('/api/mi', require('./routes/mi'));   // MI interface for advanced operations

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ 
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

// Socket.IO for real-time updates with improved error handling
io.on('connection', (socket) => {
  console.log('🔌 Client connected:', socket.id, 'from', socket.handshake.address);
  console.log('🔧 Client transport:', socket.conn.transport.name);
  console.log('🌐 Client headers:', socket.handshake.headers['user-agent']);

  // Send initial data when client connects
  socket.emit('welcome', {
    message: 'Connected to OpenSIPS UI',
    socketId: socket.id,
    timestamp: new Date().toISOString()
  });

  // Join monitoring room for real-time updates
  socket.join('monitoring');
  console.log(`Client ${socket.id} joined monitoring room`);

  // Handle client requests for real-time data
  socket.on('subscribe', (channel) => {
    if (typeof channel !== 'string') {
      console.warn(`Invalid channel type from ${socket.id}:`, typeof channel);
      return;
    }
    socket.join(channel);
    console.log(`✅ Client ${socket.id} subscribed to ${channel}`);
  });

  socket.on('unsubscribe', (channel) => {
    if (typeof channel !== 'string') {
      console.warn(`Invalid channel type from ${socket.id}:`, typeof channel);
      return;
    }
    socket.leave(channel);
    console.log(`❌ Client ${socket.id} unsubscribed from ${channel}`);
  });

  // Legacy support for old events with improved logging
  socket.on('subscribe-stats', () => {
    socket.join('stats');
    console.log(`📊 Client ${socket.id} subscribed to stats`);
  });

  socket.on('unsubscribe-stats', () => {
    socket.leave('stats');
    console.log(`📊 Client ${socket.id} unsubscribed from stats`);
  });

  socket.on('subscribe-calls', () => {
    socket.join('calls');
    console.log(`📞 Client ${socket.id} subscribed to calls`);
  });

  socket.on('unsubscribe-calls', () => {
    socket.leave('calls');
    console.log(`📞 Client ${socket.id} unsubscribed from calls`);
  });

  socket.on('subscribe-users', () => {
    socket.join('users');
    console.log(`👥 Client ${socket.id} subscribed to users`);
  });

  socket.on('unsubscribe-users', () => {
    socket.leave('users');
    console.log(`👥 Client ${socket.id} unsubscribed from users`);
  });

  // Handle connection errors
  socket.on('error', (error) => {
    console.error(`❌ Socket error from ${socket.id}:`, error);
  });

  // Handle disconnect with reason
  socket.on('disconnect', (reason) => {
    console.log(`🔌 Client ${socket.id} disconnected:`, reason);
  });

  // Handle transport upgrade
  socket.conn.on('upgrade', () => {
    console.log(`⬆️ Client ${socket.id} upgraded to:`, socket.conn.transport.name);
  });

  // Handle transport errors
  socket.conn.on('error', (error) => {
    console.error(`❌ Transport error for ${socket.id}:`, error);
  });
});

// Make io available to routes
app.set('io', io);

// Initialize real-time service
const realtimeService = new RealtimeService(io);

// Initialize event listener service for OpenSIPS events
const eventListenerService = new EventListenerService(io);

// Change detector service removed - using MI-based real-time monitoring

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 OpenSIPS UI Backend running on port ${PORT}`);
  console.log(`📊 Environment: ${process.env.NODE_ENV}`);
  console.log(`🔗 CORS Origin: ${process.env.CORS_ORIGIN}`);

  // Start real-time monitoring
  realtimeService.startMonitoring().catch(error => {
    console.error('❌ Error starting real-time monitoring:', error);
  });

  // Start event listener service
  eventListenerService.start(3002);
  console.log('📡 Event listener service started on port 3002');

  // Change detector service removed - using MI-based real-time monitoring instead
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  realtimeService.stopMonitoring();
  eventListenerService.stop();
  server.close(() => {
    console.log('Process terminated');
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  realtimeService.stopMonitoring();
  eventListenerService.stop();
  server.close(() => {
    console.log('Process terminated');
  });
});
