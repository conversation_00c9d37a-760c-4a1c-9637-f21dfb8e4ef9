const express = require('express');
const { executeQuery } = require('../config/database');

class EventListenerService {
  constructor(io) {
    this.io = io;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.server = null;
  }

  setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.urlencoded({ extended: true }));
    
    // Log all incoming events
    this.app.use((req, res, next) => {
      console.log(`📡 Event received: ${req.method} ${req.path}`);
      console.log('📡 Headers:', req.headers);
      console.log('📡 Body:', req.body);
      next();
    });
  }

  setupRoutes() {
    // OpenSIPS Event webhook endpoint
    this.app.post('/events/opensips', this.handleOpenSIPSEvent.bind(this));
    
    // Legacy events (kept for backward compatibility but not actively used)
    this.app.post('/events/user_registered', this.handleLegacyEvent.bind(this));
    this.app.post('/events/user_unregistered', this.handleLegacyEvent.bind(this));
    this.app.post('/events/call_started', this.handleLegacyEvent.bind(this));
    this.app.post('/events/call_ended', this.handleLegacyEvent.bind(this));
    this.app.post('/events/dialog_created', this.handleLegacyEvent.bind(this));
    this.app.post('/events/dialog_ended', this.handleLegacyEvent.bind(this));

    // OpenSIPS usrloc events (real-time contact events)
    this.app.post('/events/contact_insert', this.handleContactInsert.bind(this));
    this.app.post('/events/contact_delete', this.handleContactDelete.bind(this));
    this.app.post('/events/contact_update', this.handleContactUpdate.bind(this));

    // Health check
    this.app.get('/events/health', (req, res) => {
      res.json({ status: 'ok', timestamp: new Date().toISOString() });
    });
  }

  // Handle generic OpenSIPS events
  async handleOpenSIPSEvent(req, res) {
    try {
      const { event, data } = req.body;
      console.log(`📡 OpenSIPS Event: ${event}`, data);
      
      switch (event) {
        // Primary events - actively used
        case 'E_UL_CONTACT_INSERT':
          await this.processContactInsert(data);
          break;
        case 'E_UL_CONTACT_DELETE':
          await this.processContactDelete(data);
          break;
        case 'E_UL_CONTACT_UPDATE':
          await this.processContactUpdate(data);
          break;

        // Legacy events - backward compatibility only
        case 'E_USER_REGISTERED':
        case 'E_USER_UNREGISTERED':
        case 'E_CALL_STARTED':
        case 'E_CALL_ENDED':
          console.log(`📡 Legacy event received: ${event} (using contact events instead)`);
          break;

        default:
          console.log(`⚠️ Unknown event type: ${event}`);
      }
      
      res.json({ success: true, received: event });
    } catch (error) {
      console.error('❌ Error handling OpenSIPS event:', error);
      res.status(500).json({ error: 'Event processing failed' });
    }
  }

  // Legacy methods removed - using optimized contact events instead
  // All user registration/unregistration now handled by processContactInsert/Delete

  // Process contact insert event (E_UL_CONTACT_INSERT)
  async processContactInsert(data) {
    console.log('👤 Processing contact insert (registration):', data);

    const params = this.parseEventData(data);
    const { domain, aor, uri, expires, user_agent, received, socket } = params;

    // Extract username from AOR
    const username = aor ? aor.split('@')[0] : null;
    const domainName = domain || (aor ? aor.split('@')[1] : null);

    if (!username || !domainName) {
      console.warn('⚠️ Invalid AOR format:', aor);
      return;
    }

    // Get updated user data from database
    const userResult = await executeQuery(`
      SELECT
        s.username,
        s.domain,
        s.email_address,
        l.contact,
        l.expires,
        'online' as status,
        l.user_agent,
        l.last_modified as last_seen,
        l.received,
        CASE
          WHEN l.expires > UNIX_TIMESTAMP() THEN TIMESTAMPDIFF(SECOND, NOW(), FROM_UNIXTIME(l.expires))
          ELSE 0
        END as expires_in_seconds,
        FROM_UNIXTIME(l.last_modified) as last_seen_formatted,
        INET_NTOA(l.received & 0xFFFFFFFF) as ip_address
      FROM subscriber s
      INNER JOIN location l ON s.username = l.username AND s.domain = l.domain
      WHERE s.username = ? AND s.domain = ?
    `, [username, domainName]);

    if (userResult.success && userResult.data.length > 0) {
      const user = userResult.data[0];

      // Emit real-time contact insert event
      this.io.to('users').emit('contact:inserted', {
        username: user.username,
        domain: user.domain,
        aor: `${user.username}@${user.domain}`,
        contact: user.contact,
        status: 'online',
        expires_in: user.expires_in_seconds,
        user_agent: user.user_agent,
        ip_address: user.ip_address,
        timestamp: new Date().toISOString(),
        event_type: 'registration'
      });

      // Also emit user status update for compatibility
      this.io.to('users').emit('user:status', {
        username: user.username,
        domain: user.domain,
        status: 'online',
        expires_in_seconds: user.expires_in_seconds,
        last_seen: user.last_seen_formatted,
        user_agent: user.user_agent,
        ip_address: user.ip_address,
        timestamp: new Date().toISOString()
      });

      console.log(`✅ Contact insert event emitted for ${username}@${domainName}`);
    } else {
      console.warn(`⚠️ User not found in database: ${username}@${domainName}`);
    }
  }

  // Process contact delete event (E_UL_CONTACT_DELETE)
  async processContactDelete(data) {
    console.log('👤 Processing contact delete (unregistration):', data);

    const params = this.parseEventData(data);
    const { domain, aor, uri } = params;

    // Extract username from AOR
    const username = aor ? aor.split('@')[0] : null;
    const domainName = domain || (aor ? aor.split('@')[1] : null);

    if (!username || !domainName) {
      console.warn('⚠️ Invalid AOR format:', aor);
      return;
    }

    // Emit real-time contact delete event
    this.io.to('users').emit('contact:deleted', {
      username,
      domain: domainName,
      aor: `${username}@${domainName}`,
      contact: uri,
      status: 'offline',
      timestamp: new Date().toISOString(),
      event_type: 'unregistration'
    });

    // Also emit user status update for compatibility
    this.io.to('users').emit('user:status', {
      username,
      domain: domainName,
      status: 'offline',
      expires_in_seconds: 0,
      timestamp: new Date().toISOString()
    });

    console.log(`❌ Contact delete event emitted for ${username}@${domainName}`);
  }

  // Process contact update event (E_UL_CONTACT_UPDATE)
  async processContactUpdate(data) {
    console.log('👤 Processing contact update (re-registration):', data);

    const params = this.parseEventData(data);
    const { domain, aor, uri, expires, user_agent } = params;

    // Extract username from AOR
    const username = aor ? aor.split('@')[0] : null;
    const domainName = domain || (aor ? aor.split('@')[1] : null);

    if (!username || !domainName) {
      console.warn('⚠️ Invalid AOR format:', aor);
      return;
    }

    // Get updated user data from database
    const userResult = await executeQuery(`
      SELECT
        s.username,
        s.domain,
        s.email_address,
        l.contact,
        l.expires,
        'online' as status,
        l.user_agent,
        l.last_modified as last_seen,
        l.received,
        CASE
          WHEN l.expires > UNIX_TIMESTAMP() THEN TIMESTAMPDIFF(SECOND, NOW(), FROM_UNIXTIME(l.expires))
          ELSE 0
        END as expires_in_seconds,
        FROM_UNIXTIME(l.last_modified) as last_seen_formatted,
        INET_NTOA(l.received & 0xFFFFFFFF) as ip_address
      FROM subscriber s
      INNER JOIN location l ON s.username = l.username AND s.domain = l.domain
      WHERE s.username = ? AND s.domain = ?
    `, [username, domainName]);

    if (userResult.success && userResult.data.length > 0) {
      const user = userResult.data[0];

      // Emit real-time contact update event
      this.io.to('users').emit('contact:updated', {
        username: user.username,
        domain: user.domain,
        aor: `${user.username}@${user.domain}`,
        contact: user.contact,
        status: 'online',
        expires_in: user.expires_in_seconds,
        user_agent: user.user_agent,
        ip_address: user.ip_address,
        timestamp: new Date().toISOString(),
        event_type: 're-registration'
      });

      console.log(`🔄 Contact update event emitted for ${username}@${domainName}`);
    }
  }

  // Handle legacy events (simplified handler)
  async handleLegacyEvent(req, res) {
    console.log('📡 Legacy event received (deprecated):', req.path, req.body);
    res.json({
      success: true,
      message: 'Legacy event received but not processed. Use contact events instead.'
    });
  }

  // Handle OpenSIPS usrloc contact events
  async handleContactInsert(req, res) {
    await this.processContactInsert(req.body);
    res.json({ success: true });
  }

  async handleContactDelete(req, res) {
    await this.processContactDelete(req.body);
    res.json({ success: true });
  }

  async handleContactUpdate(req, res) {
    await this.processContactUpdate(req.body);
    res.json({ success: true });
  }

  // Parse OpenSIPS event data format (key=value;key=value)
  parseEventData(data) {
    if (typeof data === 'object') {
      return data;
    }
    
    const params = {};
    if (typeof data === 'string') {
      data.split(';').forEach(pair => {
        const [key, value] = pair.split('=');
        if (key && value) {
          params[key.trim()] = value.trim();
        }
      });
    }
    return params;
  }

  // Start the event listener server
  start(port = 3002) {
    this.server = this.app.listen(port, () => {
      console.log(`📡 Event Listener Service started on port ${port}`);
      console.log(`📡 OpenSIPS events endpoint: http://localhost:${port}/events/opensips`);
    });
    
    return this.server;
  }

  // Stop the event listener server
  stop() {
    if (this.server) {
      this.server.close();
      console.log('📡 Event Listener Service stopped');
    }
  }
}

module.exports = EventListenerService;
