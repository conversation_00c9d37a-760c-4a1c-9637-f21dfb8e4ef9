const axios = require('axios');

class MIService {
  constructor() {
    this.host = process.env.OPENSIPS_MI_HOST || 'opensips';
    this.port = process.env.OPENSIPS_MI_PORT || '8080';
    this.baseUrl = `http://${this.host}:${this.port}/mi`;
    this.timeout = 5000; // 5 seconds timeout
    
    console.log(`📡 MI Service initialized: ${this.baseUrl}`);
  }

  getInfo() {
    return {
      host: this.host,
      port: this.port,
      baseUrl: this.baseUrl,
      timeout: this.timeout
    };
  }

  async testConnection() {
    try {
      const response = await axios.post(this.baseUrl, {
        jsonrpc: "2.0",
        method: "uptime",
        id: "test"
      }, {
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.result) {
        console.log('✅ MI connection test successful');
        return true;
      } else {
        console.error('❌ MI connection test failed: Invalid response format');
        return false;
      }
    } catch (error) {
      console.error('❌ MI connection test failed:', error.message);
      return false;
    }
  }

  async executeCommand(command, params = []) {
    try {
      const requestId = Date.now().toString();
      const payload = {
        jsonrpc: "2.0",
        method: command,
        id: requestId
      };

      // Add parameters if provided
      if (params.length > 0) {
        if (Array.isArray(params)) {
          payload.params = params;
        } else {
          payload.params = params;
        }
      }

      const response = await axios.post(this.baseUrl, payload, {
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data && response.data.result !== undefined) {
        console.log(`📡 MI command executed: ${command}`, params.length > 0 ? params : '');
        return response.data.result;
      } else if (response.data && response.data.error) {
        throw new Error(`MI error: ${response.data.error.message || 'Unknown error'}`);
      } else {
        throw new Error('Invalid MI response format');
      }
    } catch (error) {
      console.error(`❌ MI command failed: ${command}`, error.message);
      throw new Error(`MI command failed: ${error.message}`);
    }
  }

  async getSystemStatus() {
    try {
      const uptime = await this.executeCommand('uptime');
      const version = await this.executeCommand('version');
      
      return {
        uptime,
        version,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Failed to get system status:', error.message);
      throw error;
    }
  }

  async getLocationStatistics() {
    try {
      const stats = await this.executeCommand('ul_show_contact');
      return stats;
    } catch (error) {
      console.error('❌ Failed to get location statistics:', error.message);
      throw error;
    }
  }

  async getDialogStatistics() {
    try {
      const stats = await this.executeCommand('dlg_list');
      return stats;
    } catch (error) {
      console.error('❌ Failed to get dialog statistics:', error.message);
      throw error;
    }
  }

  async getAllUsers() {
    try {
      const users = await this.executeCommand('ul_dump');
      return users;
    } catch (error) {
      console.error('❌ Failed to get all users:', error.message);
      throw error;
    }
  }

  async getAllDialogs() {
    try {
      const dialogs = await this.executeCommand('dlg_list');
      return dialogs;
    } catch (error) {
      console.error('❌ Failed to get all dialogs:', error.message);
      throw error;
    }
  }

  async getUserLocation(username) {
    try {
      const location = await this.executeCommand('ul_show_contact', [username]);
      return location;
    } catch (error) {
      console.error(`❌ Failed to get user location for ${username}:`, error.message);
      throw error;
    }
  }

  async getCallStatistics() {
    try {
      const stats = await this.executeCommand('get_statistics', ['dialog:']);
      return stats;
    } catch (error) {
      console.error('❌ Failed to get call statistics:', error.message);
      throw error;
    }
  }

  async reloadConfig() {
    try {
      const result = await this.executeCommand('reload_routes');
      console.log('🔄 OpenSIPS configuration reloaded');
      return result;
    } catch (error) {
      console.error('❌ Failed to reload configuration:', error.message);
      throw error;
    }
  }

  async getMemoryInfo() {
    try {
      const memory = await this.executeCommand('get_statistics', ['shmem:']);
      return memory;
    } catch (error) {
      console.error('❌ Failed to get memory info:', error.message);
      throw error;
    }
  }

  async getProcessInfo() {
    try {
      const processes = await this.executeCommand('ps');
      return processes;
    } catch (error) {
      console.error('❌ Failed to get process info:', error.message);
      throw error;
    }
  }
}

module.exports = MIService;
