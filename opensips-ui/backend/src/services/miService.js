const net = require('net');

class MIService {
  constructor() {
    this.host = process.env.OPENSIPS_MI_HOST || 'opensips';
    this.port = process.env.OPENSIPS_MI_PORT || 8080;
    this.timeout = 5000; // 5 seconds timeout
  }

  /**
   * Execute OpenSIPS MI command
   * @param {string} command - MI command name
   * @param {object} params - Command parameters
   * @returns {Promise<object>} - Command result
   */
  async executeCommand(command, params = {}) {
    return new Promise((resolve, reject) => {
      console.log(`🔧 Executing MI command: ${command}`, params);

      // Create HTTP request for OpenSIPS MI interface
      const http = require('http');
      
      // Build query string for parameters
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        queryParams.append(key, value);
      });
      
      const path = `/mi/${command}?${queryParams.toString()}`;
      
      const options = {
        hostname: this.host,
        port: this.port,
        path: path,
        method: 'GET',
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      console.log(`🔧 MI Request: http://${this.host}:${this.port}${path}`);

      const req = http.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            console.log(`🔧 MI Response (${res.statusCode}):`, data);
            
            if (res.statusCode === 200) {
              // Try to parse JSON response
              let result;
              try {
                result = JSON.parse(data);
              } catch (e) {
                // If not JSON, treat as plain text success
                result = { message: data.trim() };
              }
              
              resolve({
                success: true,
                data: result
              });
            } else {
              resolve({
                success: false,
                error: `HTTP ${res.statusCode}: ${data}`,
                statusCode: res.statusCode
              });
            }
          } catch (error) {
            console.error('🔧 MI Response parsing error:', error);
            resolve({
              success: false,
              error: `Response parsing error: ${error.message}`
            });
          }
        });
      });

      req.on('error', (error) => {
        console.error('🔧 MI Request error:', error);
        resolve({
          success: false,
          error: `Connection error: ${error.message}`
        });
      });

      req.on('timeout', () => {
        console.error('🔧 MI Request timeout');
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout'
        });
      });

      req.end();
    });
  }

  /**
   * Get OpenSIPS statistics
   */
  async getStatistics() {
    return this.executeCommand('get_statistics', { statistics: 'all' });
  }

  /**
   * Get active dialogs
   */
  async getDialogs() {
    return this.executeCommand('dlg_list');
  }

  /**
   * End a dialog (hangup call)
   * @param {string} callid - Call ID to hangup
   */
  async endDialog(callid) {
    return this.executeCommand('dlg_end_dlg', { callid });
  }

  /**
   * Transfer a call using REFER
   * @param {string} callid - Call ID to transfer
   * @param {string} referTo - Transfer destination
   */
  async transferCall(callid, referTo) {
    return this.executeCommand('dlg_refer', { 
      callid, 
      refer_to: referTo 
    });
  }

  /**
   * Get memory usage
   */
  async getMemoryInfo() {
    return this.executeCommand('get_statistics', { statistics: 'shmem:' });
  }

  /**
   * Reload OpenSIPS configuration
   */
  async reload() {
    return this.executeCommand('reload');
  }

  /**
   * Get OpenSIPS version
   */
  async getVersion() {
    return this.executeCommand('version');
  }

  /**
   * Check if OpenSIPS MI interface is available
   */
  async ping() {
    try {
      const result = await this.executeCommand('uptime');
      return result.success;
    } catch (error) {
      console.error('🔧 MI Ping failed:', error);
      return false;
    }
  }
}

module.exports = new MIService();
