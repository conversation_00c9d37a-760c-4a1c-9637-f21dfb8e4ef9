const net = require('net');

class MIService {
  constructor() {
    this.host = process.env.OPENSIPS_MI_HOST || 'opensips';
    this.port = process.env.OPENSIPS_MI_PORT || 8080;
    this.timeout = 5000; // 5 seconds timeout
  }

  /**
   * Execute OpenSIPS MI command
   * @param {string} command - MI command name
   * @param {object} params - Command parameters
   * @returns {Promise<object>} - Command result
   */
  async executeCommand(command, params = {}) {
    return new Promise((resolve, reject) => {
      console.log(`🔧 Executing MI command: ${command}`, params);

      // Create HTTP request for OpenSIPS MI interface
      const http = require('http');
      
      // Build query string for parameters
      const queryParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        queryParams.append(key, value);
      });
      
      const path = `/mi/${command}?${queryParams.toString()}`;
      
      const options = {
        hostname: this.host,
        port: this.port,
        path: path,
        method: 'GET',
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      };

      console.log(`🔧 MI Request: http://${this.host}:${this.port}${path}`);

      const req = http.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            console.log(`🔧 MI Response (${res.statusCode}):`, data);
            
            if (res.statusCode === 200) {
              // Try to parse JSON response
              let result;
              try {
                result = JSON.parse(data);
              } catch (e) {
                // If not JSON, treat as plain text success
                result = { message: data.trim() };
              }
              
              resolve({
                success: true,
                data: result
              });
            } else {
              resolve({
                success: false,
                error: `HTTP ${res.statusCode}: ${data}`,
                statusCode: res.statusCode
              });
            }
          } catch (error) {
            console.error('🔧 MI Response parsing error:', error);
            resolve({
              success: false,
              error: `Response parsing error: ${error.message}`
            });
          }
        });
      });

      req.on('error', (error) => {
        console.error('🔧 MI Request error:', error);
        resolve({
          success: false,
          error: `Connection error: ${error.message}`
        });
      });

      req.on('timeout', () => {
        console.error('🔧 MI Request timeout');
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout'
        });
      });

      req.end();
    });
  }

  /**
   * Get OpenSIPS statistics
   */
  async getStatistics() {
    return this.executeCommand('get_statistics', { statistics: 'all' });
  }

  /**
   * Get active dialogs
   */
  async getDialogs() {
    return this.executeCommand('dlg_list');
  }

  /**
   * End a dialog (hangup call)
   * @param {string} callid - Call ID to hangup
   */
  async endDialog(callid) {
    try {
      console.log(`🔌 Attempting to hangup call: ${callid}`);

      // First, get dialog list to find the dialog
      const dialogList = await this.executeCommand('dlg_list');
      console.log(`🔍 Dialog list result:`, dialogList);

      if (!dialogList.success) {
        return {
          success: false,
          message: 'Failed to get dialog list'
        };
      }

      // Find the dialog by callid
      let targetDialog = null;
      if (dialogList.data) {
        const dialogs = Array.isArray(dialogList.data) ? dialogList.data : [dialogList.data];
        targetDialog = dialogs.find(d => d.callid === callid);
      }

      if (!targetDialog) {
        return {
          success: false,
          message: `Dialog with callid ${callid} not found`
        };
      }

      console.log(`🎯 Found target dialog:`, targetDialog);

      // Try to hangup using dialog hash and entry
      if (targetDialog.hash && targetDialog.entry) {
        console.log(`🔌 Hanging up using hash: ${targetDialog.hash}, entry: ${targetDialog.entry}`);
        const result = await this.executeCommand('dlg_end_dlg', [targetDialog.hash, targetDialog.entry]);

        if (result.success) {
          console.log(`✅ Successfully hung up call ${callid}`);
          return {
            success: true,
            message: `Call ${callid} hung up successfully`
          };
        }
      }

      // If hash/entry method fails, try with callid directly
      console.log(`🔄 Trying direct callid method for ${callid}`);
      const directResult = await this.executeCommand('dlg_end_dlg', [callid]);

      if (directResult.success) {
        console.log(`✅ Successfully hung up call ${callid} using direct method`);
        return {
          success: true,
          message: `Call ${callid} hung up successfully`
        };
      }

      return {
        success: false,
        message: `Failed to hangup call ${callid} - all methods failed`
      };

    } catch (error) {
      console.error(`❌ Error hanging up call ${callid}:`, error);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Transfer a call using REFER
   * @param {string} callid - Call ID to transfer
   * @param {string} referTo - Transfer destination
   */
  async transferCall(callid, referTo) {
    return this.executeCommand('dlg_refer', { 
      callid, 
      refer_to: referTo 
    });
  }

  /**
   * Get memory usage
   */
  async getMemoryInfo() {
    return this.executeCommand('get_statistics', { statistics: 'shmem:' });
  }

  /**
   * Reload OpenSIPS configuration
   */
  async reload() {
    return this.executeCommand('reload');
  }

  /**
   * Get OpenSIPS version
   */
  async getVersion() {
    return this.executeCommand('version');
  }

  /**
   * Check if OpenSIPS MI interface is available
   */
  async ping() {
    try {
      const result = await this.executeCommand('uptime');
      return result.success;
    } catch (error) {
      console.error('🔧 MI Ping failed:', error);
      return false;
    }
  }
}

module.exports = new MIService();
