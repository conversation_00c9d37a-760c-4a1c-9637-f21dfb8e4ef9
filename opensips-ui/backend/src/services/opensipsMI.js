const axios = require('axios');

class OpenSIPSMIService {
  constructor() {
    this.miUrl = process.env.OPENSIPS_MI_URL || 'http://***********:8080/mi';
    this.timeout = 5000;
    this.lastStats = {};
    this.lastLocationStats = {};
  }

  // Send MI command to OpenSIPS
  async sendMICommand(command, params = null) {
    try {
      const payload = {
        jsonrpc: "2.0",
        id: 1,
        method: command
      };

      if (params) {
        payload.params = params;
      }

      console.log(`📡 Sending MI command: ${command}`, params ? params : '');

      const response = await axios.post(this.miUrl, payload, {
        timeout: this.timeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.data.error) {
        console.error(`❌ MI command error:`, response.data.error);
        return { success: false, error: response.data.error };
      }

      return { success: true, data: response.data.result };
    } catch (error) {
      console.error(`❌ MI command failed for ${command}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  // Get all statistics from OpenSIPS
  async getStatistics(filter = 'all') {
    const params = {
      statistics: Array.isArray(filter) ? filter : [filter]
    };

    const result = await this.sendMICommand('get_statistics', params);
    if (result.success) {
      console.log(`📊 Retrieved ${Object.keys(result.data).length} statistics`);
      return result;
    }
    return result;
  }

  // Get location statistics (user registrations)
  async getLocationStatistics() {
    const result = await this.getStatistics(['usrloc:']);
    if (result.success) {
      const locationStats = {};
      Object.keys(result.data).forEach(key => {
        if (key.startsWith('usrloc:')) {
          const statName = key.replace('usrloc:', '');
          locationStats[statName] = result.data[key];
        }
      });
      return { success: true, data: locationStats };
    }
    return result;
  }

  // Get dialog statistics (active calls)
  async getDialogStatistics() {
    const result = await this.getStatistics(['dialog:']);
    if (result.success) {
      const dialogStats = {};
      Object.keys(result.data).forEach(key => {
        if (key.startsWith('dialog:')) {
          const statName = key.replace('dialog:', '');
          dialogStats[statName] = result.data[key];
        }
      });
      return { success: true, data: dialogStats };
    }
    return result;
  }

  // Get core statistics
  async getCoreStatistics() {
    const result = await this.getStatistics(['core:']);
    if (result.success) {
      const coreStats = {};
      Object.keys(result.data).forEach(key => {
        if (key.startsWith('core:')) {
          const statName = key.replace('core:', '');
          coreStats[statName] = result.data[key];
        }
      });
      return { success: true, data: coreStats };
    }
    return result;
  }

  // Get TM (Transaction Manager) statistics
  async getTMStatistics() {
    const result = await this.getStatistics(['tm:']);
    if (result.success) {
      const tmStats = {};
      Object.keys(result.data).forEach(key => {
        if (key.startsWith('tm:')) {
          const statName = key.replace('tm:', '');
          tmStats[statName] = result.data[key];
        }
      });
      return { success: true, data: tmStats };
    }
    return result;
  }

  // Get list of available statistics
  async listStatistics() {
    const result = await this.sendMICommand('list_statistics');
    if (result.success) {
      console.log(`📋 Available statistics:`, Object.keys(result.data).length);
      return result;
    }
    return result;
  }

  // Reset specific statistics
  async resetStatistics(stats) {
    const params = {
      statistics: Array.isArray(stats) ? stats : [stats]
    };

    const result = await this.sendMICommand('reset_statistics', params);
    if (result.success) {
      console.log(`🔄 Reset statistics:`, params.statistics);
    }
    return result;
  }

  // Get registered users (ul_show)
  async getRegisteredUsers(table = 'location') {
    const result = await this.sendMICommand('ul_show', { table });
    if (result.success) {
      console.log(`👥 Retrieved registered users from table: ${table}`);
      return result;
    }
    return result;
  }

  // Get active dialogs
  async getActiveDialogs() {
    const result = await this.sendMICommand('dlg_list');
    if (result.success) {
      console.log(`📞 Retrieved active dialogs`);
      return result;
    }
    return result;
  }

  // Detect changes in statistics
  detectStatisticsChanges(newStats, category = 'general') {
    const changes = [];
    const lastStats = this.lastStats[category] || {};

    Object.keys(newStats).forEach(key => {
      const newValue = newStats[key];
      const oldValue = lastStats[key];

      if (oldValue !== undefined && oldValue !== newValue) {
        changes.push({
          statistic: key,
          oldValue,
          newValue,
          change: newValue - oldValue,
          category
        });
      }
    });

    // Update last stats
    this.lastStats[category] = { ...newStats };

    return changes;
  }

  // Detect location changes (user registrations)
  async detectLocationChanges() {
    const result = await this.getLocationStatistics();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    const changes = this.detectStatisticsChanges(result.data, 'location');
    
    // Look for specific registration changes
    const registrationChanges = [];
    
    changes.forEach(change => {
      if (change.statistic === 'registered_users' && change.change !== 0) {
        registrationChanges.push({
          type: change.change > 0 ? 'registration' : 'unregistration',
          count: Math.abs(change.change),
          timestamp: new Date().toISOString()
        });
      }
    });

    return {
      success: true,
      data: {
        statisticsChanges: changes,
        registrationChanges,
        currentStats: result.data
      }
    };
  }

  // Detect dialog changes (call events)
  async detectDialogChanges() {
    const result = await this.getDialogStatistics();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    const changes = this.detectStatisticsChanges(result.data, 'dialog');
    
    // Look for specific call changes
    const callChanges = [];
    
    changes.forEach(change => {
      if (change.statistic === 'active_dialogs' && change.change !== 0) {
        callChanges.push({
          type: change.change > 0 ? 'call_started' : 'call_ended',
          count: Math.abs(change.change),
          timestamp: new Date().toISOString()
        });
      }
    });

    return {
      success: true,
      data: {
        statisticsChanges: changes,
        callChanges,
        currentStats: result.data
      }
    };
  }

  // Get comprehensive system status
  async getSystemStatus() {
    try {
      const [locationResult, dialogResult, coreResult, tmResult] = await Promise.all([
        this.getLocationStatistics(),
        this.getDialogStatistics(),
        this.getCoreStatistics(),
        this.getTMStatistics()
      ]);

      const status = {
        timestamp: new Date().toISOString(),
        location: locationResult.success ? locationResult.data : {},
        dialog: dialogResult.success ? dialogResult.data : {},
        core: coreResult.success ? coreResult.data : {},
        tm: tmResult.success ? tmResult.data : {}
      };

      return { success: true, data: status };
    } catch (error) {
      console.error('❌ Error getting system status:', error);
      return { success: false, error: error.message };
    }
  }

  // Test MI connection
  async testConnection() {
    console.log(`🔗 Testing MI connection to: ${this.miUrl}`);
    const result = await this.sendMICommand('uptime');
    if (result.success) {
      console.log(`✅ MI connection successful. Uptime: ${result.data} seconds`);
      return true;
    } else {
      console.error(`❌ MI connection failed:`, result.error);
      return false;
    }
  }

  // Get service info
  getInfo() {
    return {
      miUrl: this.miUrl,
      timeout: this.timeout,
      lastStatsCategories: Object.keys(this.lastStats)
    };
  }
}

module.exports = OpenSIPSMIService;
