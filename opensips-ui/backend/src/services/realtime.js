const { executeQuery } = require('../config/database');
const OpenSIPSMIService = require('./opensipsMI');

class RealtimeService {
  constructor(io) {
    this.io = io;
    this.intervals = new Map();
    this.lastStats = {};
    this.lastUsers = {};
    this.lastCalls = {};

    // Initialize OpenSIPS MI Service
    this.miService = new OpenSIPSMIService();

    // Track last stats for instant change detection
    this.lastLocationStats = null;
    this.lastDialogStats = null;
  }

  // Start real-time monitoring
  async startMonitoring() {
    console.log('🔄 Starting real-time monitoring services...');

    // Test MI connection first
    const miConnected = await this.miService.testConnection();
    if (miConnected) {
      console.log('✅ OpenSIPS MI connection established');

      // Start MI-based monitoring (very fast intervals for real-time feel)
      this.startMILocationMonitoring();
      this.startMIDialogMonitoring();

      // Start ultra-fast change detection
      this.startUltraFastMonitoring();
    } else {
      console.log('⚠️ MI connection failed, falling back to database polling');
    }

    // Monitor system stats every 10 seconds
    this.startStatsMonitoring();

    // Monitor user registrations every 15 seconds
    this.startUserMonitoring();

    // Monitor active calls every 5 seconds
    this.startCallMonitoring();

    // Monitor events every 30 seconds
    this.startEventMonitoring();
  }

  // Stop all monitoring
  stopMonitoring() {
    console.log('⏹️ Stopping real-time monitoring services...');
    this.intervals.forEach((interval) => {
      clearInterval(interval);
    });
    this.intervals.clear();
  }

  // Monitor system statistics with improved error handling
  startStatsMonitoring() {
    const interval = setInterval(async () => {
      try {
        const stats = await this.getRealtimeStats();

        // Only emit if stats changed significantly
        if (this.hasStatsChanged(stats)) {
          const updateData = {
            type: 'stats',
            data: stats,
            timestamp: new Date().toISOString()
          };

          this.io.to('stats').emit('stats-update', updateData);
          console.log('📊 Stats update emitted to', this.io.sockets.adapter.rooms.get('stats')?.size || 0, 'clients');
          this.lastStats = stats;
        }
      } catch (error) {
        console.error('❌ Error in stats monitoring:', error);
        // Emit error to monitoring clients
        this.io.to('monitoring').emit('monitoring-error', {
          type: 'stats',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }, 3000); // Every 3 seconds for faster stats updates

    this.intervals.set('stats', interval);
    console.log('📊 Stats monitoring started');
  }

  // Monitor user registrations with improved error handling and detailed data
  startUserMonitoring() {
    const interval = setInterval(async () => {
      try {
        const users = await this.getDetailedUsers();

        // Check for registration changes
        if (this.hasUsersChanged(users)) {
          const total = users.length;
          const online = users.filter(u => u.status === 'online').length;
          const offline = total - online;

          const updateData = {
            type: 'users',
            data: {
              users,
              total,
              online,
              offline,
              timestamp: new Date().toISOString()
            },
            timestamp: new Date().toISOString()
          };

          this.io.to('users').emit('users-update', updateData);
          console.log('👥 Users update emitted to', this.io.sockets.adapter.rooms.get('users')?.size || 0, 'clients');
          console.log(`👥 Status: ${online}/${total} online`);

          // Emit individual user status changes for granular updates
          this.emitUserStatusChanges(users);

          this.lastUsers = users;
        }
      } catch (error) {
        console.error('❌ Error in user monitoring:', error);
        // Emit error to monitoring clients
        this.io.to('monitoring').emit('monitoring-error', {
          type: 'users',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }, 2000); // Every 2 seconds for ultra-responsive updates

    this.intervals.set('users', interval);
    console.log('👥 User monitoring started');
  }

  // Monitor active calls with improved error handling
  startCallMonitoring() {
    const interval = setInterval(async () => {
      try {
        const calls = await this.getActiveCalls();

        // Check for call changes
        if (this.hasCallsChanged(calls)) {
          const updateData = {
            type: 'calls',
            data: calls,
            timestamp: new Date().toISOString()
          };

          this.io.to('calls').emit('calls-update', updateData);
          console.log('📞 Calls update emitted to', this.io.sockets.adapter.rooms.get('calls')?.size || 0, 'clients');
          this.lastCalls = calls;
        }
      } catch (error) {
        console.error('❌ Error in call monitoring:', error);
        // Emit error to monitoring clients
        this.io.to('monitoring').emit('monitoring-error', {
          type: 'calls',
          error: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }, 5000); // Every 5 seconds

    this.intervals.set('calls', interval);
    console.log('📞 Call monitoring started');
  }

  // Monitor recent events
  startEventMonitoring() {
    const interval = setInterval(async () => {
      try {
        const events = await this.getRecentEvents();
        
        // Always emit recent events for activity feed
        this.io.to('monitoring').emit('events-update', {
          type: 'events',
          data: events,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error in event monitoring:', error);
      }
    }, 30000); // Every 30 seconds

    this.intervals.set('events', interval);
  }

  // Get real-time statistics with enhanced metrics
  async getRealtimeStats() {
    try {
      const queries = [
        'SELECT COUNT(*) as active_registrations FROM location WHERE expires > UNIX_TIMESTAMP()',
        'SELECT COUNT(*) as active_dialogs FROM dialog WHERE state = 4',
        'SELECT COUNT(*) as calls_today FROM acc WHERE DATE(FROM_UNIXTIME(time)) = CURDATE() AND method = "INVITE"',
        'SELECT COUNT(*) as calls_last_hour FROM acc WHERE time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR)) AND method = "INVITE"',
        'SELECT COUNT(*) as total_users FROM subscriber',
        'SELECT COUNT(*) as failed_calls_today FROM acc WHERE DATE(FROM_UNIXTIME(time)) = CURDATE() AND method = "INVITE" AND sip_code >= 400',
        'SELECT AVG(duration) as avg_duration FROM acc WHERE DATE(FROM_UNIXTIME(time)) = CURDATE() AND method = "BYE" AND duration > 0',
        'SELECT COUNT(*) as calls_this_month FROM acc WHERE YEAR(FROM_UNIXTIME(time)) = YEAR(CURDATE()) AND MONTH(FROM_UNIXTIME(time)) = MONTH(CURDATE()) AND method = "INVITE"',
        'SELECT COUNT(*) as registrations_today FROM location WHERE DATE(FROM_UNIXTIME(last_modified)) = CURDATE()'
      ];

      const results = await Promise.all(
        queries.map(query => executeQuery(query).catch(err => {
          console.error(`Query failed: ${query}`, err);
          return { success: false, data: [{ [query.split(' ')[1].toLowerCase()]: 0 }] };
        }))
      );

      const callsToday = results[2].success ? results[2].data[0].calls_today : 0;
      const failedCallsToday = results[5].success ? results[5].data[0].failed_calls_today : 0;
      const avgDuration = results[6].success ? results[6].data[0].avg_duration : 0;

      const stats = {
        activeRegistrations: results[0].success ? results[0].data[0].active_registrations : 0,
        activeDialogs: results[1].success ? results[1].data[0].active_dialogs : 0,
        callsToday: callsToday,
        callsLastHour: results[3].success ? results[3].data[0].calls_last_hour : 0,
        totalUsers: results[4].success ? results[4].data[0].total_users : 0,
        failedCallsToday: failedCallsToday,
        successRate: callsToday > 0 ? parseFloat(((callsToday - failedCallsToday) / callsToday * 100).toFixed(2)) : 100,
        avgCallDuration: avgDuration ? Math.round(avgDuration) : 0,
        callsThisMonth: results[7].success ? results[7].data[0].calls_this_month : 0,
        registrationsToday: results[8].success ? results[8].data[0].registrations_today : 0,
        timestamp: new Date().toISOString()
      };

      console.log('📊 Real-time stats:', stats);
      return stats;
    } catch (error) {
      console.error('Error getting real-time stats:', error);
      return {
        activeRegistrations: 0,
        activeDialogs: 0,
        callsToday: 0,
        callsLastHour: 0,
        totalUsers: 0,
        failedCallsToday: 0,
        successRate: 100,
        avgCallDuration: 0,
        callsThisMonth: 0,
        registrationsToday: 0,
        timestamp: new Date().toISOString()
      };
    }
  }

  // Get online users (legacy method)
  async getOnlineUsers() {
    const result = await executeQuery(`
      SELECT
        s.username,
        s.domain,
        l.contact,
        l.expires,
        FROM_UNIXTIME(l.expires) as expires_at
      FROM subscriber s
      INNER JOIN location l ON s.username = l.username AND s.domain = l.domain
      WHERE l.expires > UNIX_TIMESTAMP()
      ORDER BY s.username
    `);

    return result.success ? result.data : [];
  }

  // Get detailed users with all information for real-time updates
  async getDetailedUsers() {
    const result = await executeQuery(`
      SELECT
        s.username,
        s.domain,
        s.email_address,
        s.ha1 as password_hash,
        l.contact,
        l.expires,
        CASE
          WHEN l.expires > UNIX_TIMESTAMP() THEN 'online'
          ELSE 'offline'
        END as status,
        l.user_agent,
        l.last_modified as last_seen,
        l.received,
        l.path,
        l.socket,
        l.methods,
        l.cseq,
        l.q as priority,
        CASE
          WHEN l.expires > UNIX_TIMESTAMP() THEN TIMESTAMPDIFF(SECOND, NOW(), FROM_UNIXTIME(l.expires))
          ELSE 0
        END as expires_in_seconds,
        FROM_UNIXTIME(l.last_modified) as last_seen_formatted,
        INET_NTOA(l.received & 0xFFFFFFFF) as ip_address
      FROM subscriber s
      LEFT JOIN location l ON s.username = l.username AND s.domain = l.domain
      ORDER BY
        CASE WHEN l.expires > UNIX_TIMESTAMP() THEN 0 ELSE 1 END,
        s.username
    `);

    return result.success ? result.data : [];
  }

  // Get active calls
  async getActiveCalls() {
    const result = await executeQuery(`
      SELECT 
        dlg_id,
        callid,
        from_uri,
        to_uri,
        state,
        start_time,
        FROM_UNIXTIME(start_time) as started_at,
        TIMESTAMPDIFF(SECOND, FROM_UNIXTIME(start_time), NOW()) as duration
      FROM dialog 
      WHERE state = 4
      ORDER BY start_time DESC
    `);

    return result.success ? result.data : [];
  }

  // Get recent events
  async getRecentEvents() {
    const callEvents = await executeQuery(`
      SELECT 
        'call' as type,
        method,
        from_tag as source,
        to_tag as destination,
        sip_code,
        sip_reason,
        time as timestamp,
        FROM_UNIXTIME(time) as event_time
      FROM acc 
      WHERE time > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR))
      ORDER BY time DESC 
      LIMIT 20
    `);

    const regEvents = await executeQuery(`
      SELECT 
        'registration' as type,
        username as source,
        contact as destination,
        'Registration' as method,
        200 as sip_code,
        'OK' as sip_reason,
        last_modified as timestamp,
        FROM_UNIXTIME(last_modified) as event_time
      FROM location 
      WHERE last_modified > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 HOUR))
      ORDER BY last_modified DESC 
      LIMIT 10
    `);

    const events = [];
    if (callEvents.success) events.push(...callEvents.data);
    if (regEvents.success) events.push(...regEvents.data);

    // Sort by timestamp
    events.sort((a, b) => b.timestamp - a.timestamp);

    return events.slice(0, 30);
  }

  // Check if stats changed significantly
  hasStatsChanged(newStats) {
    if (!this.lastStats || Object.keys(this.lastStats).length === 0) {
      return true;
    }

    // Check for any changes in key metrics
    const keys = [
      'activeRegistrations',
      'activeDialogs',
      'callsToday',
      'callsLastHour',
      'failedCallsToday',
      'callsThisMonth',
      'registrationsToday'
    ];
    return keys.some(key => this.lastStats[key] !== newStats[key]);
  }

  // Check if users changed (enhanced to detect status and expiration changes)
  hasUsersChanged(newUsers) {
    console.log('🔍 Checking if users changed...');
    console.log('🔍 New users count:', newUsers.length);
    console.log('🔍 Last users count:', this.lastUsers ? this.lastUsers.length : 0);

    if (!this.lastUsers || this.lastUsers.length !== newUsers.length) {
      console.log('✅ Users changed: different count');
      return true;
    }

    // Check for changes in user list, status, or expiration times
    for (let i = 0; i < newUsers.length; i++) {
      const newUser = newUsers[i];
      const lastUser = this.lastUsers.find(u => u.username === newUser.username && u.domain === newUser.domain);

      if (!lastUser) {
        console.log(`✅ Users changed: new user ${newUser.username}`);
        return true; // New user
      }

      // Check for status changes
      if (lastUser.status !== newUser.status) {
        console.log(`✅ Users changed: ${newUser.username} status ${lastUser.status} -> ${newUser.status}`);
        return true;
      }

      // Check for significant expiration time changes (more than 10 seconds difference)
      const expDiff = Math.abs((lastUser.expires_in_seconds || 0) - (newUser.expires_in_seconds || 0));
      if (expDiff > 10) {
        console.log(`✅ Users changed: ${newUser.username} expires ${lastUser.expires_in_seconds}s -> ${newUser.expires_in_seconds}s (diff: ${expDiff}s)`);
        return true;
      }

      // Check for user agent changes
      if (lastUser.user_agent !== newUser.user_agent) {
        console.log(`✅ Users changed: ${newUser.username} user_agent changed`);
        return true;
      }
    }

    console.log('❌ No users changes detected');
    return false;
  }

  // Check if calls changed
  hasCallsChanged(newCalls) {
    if (!this.lastCalls || this.lastCalls.length !== newCalls.length) {
      return true;
    }

    // Check for changes in call list
    const lastCallIds = this.lastCalls.map(c => c.callid).sort();
    const newCallIds = newCalls.map(c => c.callid).sort();

    return JSON.stringify(lastCallIds) !== JSON.stringify(newCallIds);
  }

  // Emit individual user status changes for granular real-time updates
  emitUserStatusChanges(users) {
    console.log('🔄 Emitting individual user status changes for', users.length, 'users');

    users.forEach(user => {
      // Emit individual user status for real-time status indicators
      const statusData = {
        username: user.username,
        domain: user.domain,
        status: user.status,
        expires_in_seconds: user.expires_in_seconds,
        last_seen: user.last_seen_formatted,
        user_agent: user.user_agent,
        ip_address: user.ip_address,
        timestamp: new Date().toISOString()
      };

      this.io.to('users').emit('user:status', statusData);
      console.log(`👤 Emitted user:status for ${user.username}: ${user.status} (expires in ${user.expires_in_seconds}s)`);

      // Emit user registration events
      if (user.status === 'online' && user.expires_in_seconds > 0) {
        const regData = {
          username: user.username,
          domain: user.domain,
          expires_in: user.expires_in_seconds,
          user_agent: user.user_agent,
          contact: user.contact,
          timestamp: new Date().toISOString()
        };

        this.io.to('users').emit('user:registration', regData);
        console.log(`📝 Emitted user:registration for ${user.username}`);
      }

      // Emit user expiration warnings (when expires in less than 60 seconds)
      if (user.status === 'online' && user.expires_in_seconds > 0 && user.expires_in_seconds < 60) {
        const expData = {
          username: user.username,
          domain: user.domain,
          expires_in: user.expires_in_seconds,
          timestamp: new Date().toISOString()
        };

        this.io.to('users').emit('user:expiring', expData);
        console.log(`⏰ Emitted user:expiring for ${user.username} (${user.expires_in_seconds}s left)`);
      }
    });
  }

  // Emit specific event
  emitEvent(room, event, data) {
    this.io.to(room).emit(event, {
      ...data,
      timestamp: new Date().toISOString()
    });
  }

  // Emit to all monitoring clients
  emitToMonitoring(event, data) {
    this.emitEvent('monitoring', event, data);
  }
  // Start MI-based location monitoring
  startMILocationMonitoring() {
    console.log('📡 Starting MI location monitoring (500ms interval)');
    this.intervals.set('mi_location', setInterval(async () => {
      await this.monitorLocationChangesViaMI();
    }, 500)); // 500ms for near real-time
  }

  // Start MI-based dialog monitoring
  startMIDialogMonitoring() {
    console.log('📡 Starting MI dialog monitoring (500ms interval)');
    this.intervals.set('mi_dialog', setInterval(async () => {
      await this.monitorDialogChangesViaMI();
    }, 500)); // 500ms for near real-time
  }

  // Start ultra-fast monitoring for immediate detection
  startUltraFastMonitoring() {
    console.log('⚡ Starting ultra-fast monitoring (200ms interval)');
    this.intervals.set('ultra_fast', setInterval(async () => {
      await this.ultraFastChangeDetection();
    }, 200)); // 200ms for ultra-fast detection
  }

  // Monitor location changes via MI
  async monitorLocationChangesViaMI() {
    try {
      const result = await this.miService.detectLocationChanges();
      if (result.success) {
        const { registrationChanges, currentStats } = result.data;

        // Emit registration events
        registrationChanges.forEach(change => {
          if (change.type === 'registration') {
            this.io.to('users').emit('user:registered', {
              count: change.count,
              timestamp: change.timestamp,
              stats: currentStats
            });
            console.log(`👤 User registration detected: +${change.count} users`);
          } else if (change.type === 'unregistration') {
            this.io.to('users').emit('user:unregistered', {
              count: change.count,
              timestamp: change.timestamp,
              stats: currentStats
            });
            console.log(`👤 User unregistration detected: -${change.count} users`);
          }
        });

        // Emit general location stats update if there are changes
        if (registrationChanges.length > 0) {
          this.io.to('users').emit('location:stats', {
            stats: currentStats,
            timestamp: new Date().toISOString()
          });

          // Trigger full user list update
          this.monitorUsers();
        }
      }
    } catch (error) {
      console.error('❌ Error monitoring location changes via MI:', error);
    }
  }

  // Monitor dialog changes via MI
  async monitorDialogChangesViaMI() {
    try {
      const result = await this.miService.detectDialogChanges();
      if (result.success) {
        const { callChanges, currentStats } = result.data;

        // Emit call events
        callChanges.forEach(change => {
          if (change.type === 'call_started') {
            this.io.to('calls').emit('call:started', {
              count: change.count,
              timestamp: change.timestamp,
              stats: currentStats
            });
            console.log(`📞 Call started detected: +${change.count} calls`);
          } else if (change.type === 'call_ended') {
            this.io.to('calls').emit('call:ended', {
              count: change.count,
              timestamp: change.timestamp,
              stats: currentStats
            });
            console.log(`📞 Call ended detected: -${change.count} calls`);
          }
        });

        // Emit general dialog stats update if there are changes
        if (callChanges.length > 0) {
          this.io.to('calls').emit('dialog:stats', {
            stats: currentStats,
            timestamp: new Date().toISOString()
          });

          // Trigger full call list update
          this.monitorCalls();
        }
      }
    } catch (error) {
      console.error('❌ Error monitoring dialog changes via MI:', error);
    }
  }

  // Get MI service info
  getMIServiceInfo() {
    return this.miService.getInfo();
  }

  // Test MI connection manually
  async testMIConnection() {
    return await this.miService.testConnection();
  }

  // Get system status via MI
  async getSystemStatusViaMI() {
    return await this.miService.getSystemStatus();
  }

  // Ultra-fast change detection
  async ultraFastChangeDetection() {
    try {
      // Get current statistics quickly
      const [locationResult, dialogResult] = await Promise.all([
        this.miService.getLocationStatistics(),
        this.miService.getDialogStatistics()
      ]);

      if (locationResult.success && dialogResult.success) {
        const locationStats = locationResult.data;
        const dialogStats = dialogResult.data;

        // Check for immediate changes
        const currentTime = Date.now();

        // Location changes detection
        if (this.lastLocationStats) {
          const userCountChange = locationStats.registered_users - this.lastLocationStats.registered_users;
          const contactCountChange = locationStats['location-contacts'] - this.lastLocationStats['location-contacts'];

          if (userCountChange !== 0 || contactCountChange !== 0) {
            console.log(`⚡ INSTANT USER CHANGE: Users ${userCountChange > 0 ? '+' : ''}${userCountChange}, Contacts ${contactCountChange > 0 ? '+' : ''}${contactCountChange}`);

            // Emit immediate event
            this.io.to('users').emit('user:instant_change', {
              type: userCountChange > 0 ? 'registration' : 'unregistration',
              userChange: userCountChange,
              contactChange: contactCountChange,
              currentUsers: locationStats.registered_users,
              currentContacts: locationStats['location-contacts'],
              timestamp: new Date().toISOString(),
              detectionTime: currentTime
            });

            // Trigger immediate full refresh
            this.monitorUsers();
          }
        }

        // Dialog changes detection
        if (this.lastDialogStats) {
          const activeCallsChange = dialogStats.active_dialogs - this.lastDialogStats.active_dialogs;
          const earlyCallsChange = dialogStats.early_dialogs - this.lastDialogStats.early_dialogs;

          if (activeCallsChange !== 0 || earlyCallsChange !== 0) {
            console.log(`⚡ INSTANT CALL CHANGE: Active ${activeCallsChange > 0 ? '+' : ''}${activeCallsChange}, Early ${earlyCallsChange > 0 ? '+' : ''}${earlyCallsChange}`);

            // Emit immediate event
            this.io.to('calls').emit('call:instant_change', {
              type: activeCallsChange > 0 ? 'call_started' : 'call_ended',
              activeChange: activeCallsChange,
              earlyChange: earlyCallsChange,
              currentActive: dialogStats.active_dialogs,
              currentEarly: dialogStats.early_dialogs,
              timestamp: new Date().toISOString(),
              detectionTime: currentTime
            });

            // Trigger immediate full refresh
            this.monitorCalls();
          }
        }

        // Update last stats
        this.lastLocationStats = { ...locationStats };
        this.lastDialogStats = { ...dialogStats };
      }
    } catch (error) {
      // Silently handle errors to avoid spam
      if (error.message.includes('ECONNREFUSED')) {
        // OpenSIPS might be restarting, skip this cycle
        return;
      }
      console.error('❌ Ultra-fast detection error:', error.message);
    }
  }
}

module.exports = RealtimeService;
