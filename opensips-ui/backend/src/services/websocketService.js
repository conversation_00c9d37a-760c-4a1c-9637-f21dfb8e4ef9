const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const { pool } = require('../config/database');

class WebSocketService {
  constructor() {
    this.wss = null;
    this.clients = new Map(); // Store authenticated clients
    this.intervals = new Map(); // Store intervals for each client
  }

  initialize(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws'
    });

    this.wss.on('connection', (ws, req) => {
      console.log('🔌 New WebSocket connection');
      
      ws.on('message', async (message) => {
        try {
          const data = JSON.parse(message);
          await this.handleMessage(ws, data);
        } catch (error) {
          console.error('❌ WebSocket message error:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
        this.handleDisconnect(ws);
      });

      ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        this.handleDisconnect(ws);
      });
    });

    console.log('🔌 WebSocket server initialized');
  }

  async handleMessage(ws, data) {
    const { type, token, payload } = data;

    switch (type) {
      case 'auth':
        await this.handleAuth(ws, token);
        break;
      
      case 'subscribe':
        await this.handleSubscribe(ws, payload);
        break;
      
      case 'unsubscribe':
        await this.handleUnsubscribe(ws, payload);
        break;
      
      default:
        ws.send(JSON.stringify({
          type: 'error',
          message: 'Unknown message type'
        }));
    }
  }

  async handleAuth(ws, token) {
    try {
      if (!token) {
        throw new Error('No token provided');
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
      
      // Store client info
      const clientId = this.generateClientId();
      this.clients.set(clientId, {
        ws,
        userId: decoded.userId,
        username: decoded.username,
        role: decoded.role,
        subscriptions: new Set(),
        lastSeen: Date.now()
      });

      ws.clientId = clientId;

      ws.send(JSON.stringify({
        type: 'auth_success',
        clientId,
        message: 'Authentication successful'
      }));

      console.log(`🔌 Client ${decoded.username} authenticated`);

    } catch (error) {
      console.error('❌ WebSocket auth error:', error);
      ws.send(JSON.stringify({
        type: 'auth_error',
        message: 'Authentication failed'
      }));
      ws.close();
    }
  }

  async handleSubscribe(ws, payload) {
    const client = this.getClientByWs(ws);
    if (!client) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Not authenticated'
      }));
      return;
    }

    const { channel } = payload;
    
    if (!this.isValidChannel(channel)) {
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Invalid channel'
      }));
      return;
    }

    client.subscriptions.add(channel);
    
    ws.send(JSON.stringify({
      type: 'subscribed',
      channel,
      message: `Subscribed to ${channel}`
    }));

    // Start sending updates for this channel
    this.startChannelUpdates(client, channel);

    console.log(`🔌 Client ${client.username} subscribed to ${channel}`);
  }

  async handleUnsubscribe(ws, payload) {
    const client = this.getClientByWs(ws);
    if (!client) return;

    const { channel } = payload;
    client.subscriptions.delete(channel);
    
    // Stop updates for this channel if no more subscribers
    this.stopChannelUpdates(client, channel);

    ws.send(JSON.stringify({
      type: 'unsubscribed',
      channel,
      message: `Unsubscribed from ${channel}`
    }));

    console.log(`🔌 Client ${client.username} unsubscribed from ${channel}`);
  }

  handleDisconnect(ws) {
    const client = this.getClientByWs(ws);
    if (client) {
      // Clear all intervals for this client
      const intervalKey = ws.clientId;
      if (this.intervals.has(intervalKey)) {
        clearInterval(this.intervals.get(intervalKey));
        this.intervals.delete(intervalKey);
      }
      
      this.clients.delete(ws.clientId);
      console.log(`🔌 Client ${client.username} disconnected`);
    }
  }

  startChannelUpdates(client, channel) {
    const intervalKey = `${client.ws.clientId}_${channel}`;
    
    // Clear existing interval if any
    if (this.intervals.has(intervalKey)) {
      clearInterval(this.intervals.get(intervalKey));
    }

    let interval;
    
    switch (channel) {
      case 'active_calls':
        interval = setInterval(async () => {
          await this.sendActiveCalls(client);
        }, 5000); // Update every 5 seconds
        break;
      
      case 'system_stats':
        interval = setInterval(async () => {
          await this.sendSystemStats(client);
        }, 10000); // Update every 10 seconds
        break;
      
      case 'call_events':
        interval = setInterval(async () => {
          await this.sendCallEvents(client);
        }, 2000); // Update every 2 seconds
        break;
    }

    if (interval) {
      this.intervals.set(intervalKey, interval);
    }
  }

  stopChannelUpdates(client, channel) {
    const intervalKey = `${client.ws.clientId}_${channel}`;
    if (this.intervals.has(intervalKey)) {
      clearInterval(this.intervals.get(intervalKey));
      this.intervals.delete(intervalKey);
    }
  }

  async sendActiveCalls(client) {
    try {
      const [calls] = await pool.execute(`
        SELECT 
          callid,
          from_uri,
          to_uri,
          start_time,
          state,
          UNIX_TIMESTAMP() - start_time as duration
        FROM dialog 
        WHERE state = 4
        ORDER BY start_time DESC
      `);

      client.ws.send(JSON.stringify({
        type: 'active_calls_update',
        data: calls,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('❌ Error sending active calls:', error);
    }
  }

  async sendSystemStats(client) {
    try {
      // Get basic stats
      const [totalCalls] = await pool.execute(`
        SELECT COUNT(*) as count FROM acc 
        WHERE method = 'INVITE' AND time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
      `);
      
      const [activeCalls] = await pool.execute(`
        SELECT COUNT(*) as count FROM dialog WHERE state = 4
      `);

      const stats = {
        totalCallsLastHour: totalCalls[0].count,
        activeCalls: activeCalls[0].count,
        timestamp: Date.now()
      };

      client.ws.send(JSON.stringify({
        type: 'system_stats_update',
        data: stats,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('❌ Error sending system stats:', error);
    }
  }

  async sendCallEvents(client) {
    try {
      // Get recent call events (last 5 minutes)
      const [events] = await pool.execute(`
        SELECT 
          method,
          from_tag as caller,
          to_tag as callee,
          sip_code,
          sip_reason,
          time,
          duration
        FROM acc 
        WHERE time >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
        ORDER BY time DESC
        LIMIT 10
      `);

      client.ws.send(JSON.stringify({
        type: 'call_events_update',
        data: events,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('❌ Error sending call events:', error);
    }
  }

  isValidChannel(channel) {
    const validChannels = ['active_calls', 'system_stats', 'call_events'];
    return validChannels.includes(channel);
  }

  getClientByWs(ws) {
    return ws.clientId ? this.clients.get(ws.clientId) : null;
  }

  generateClientId() {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  // Broadcast to all clients subscribed to a channel
  broadcast(channel, data) {
    this.clients.forEach((client) => {
      if (client.subscriptions.has(channel) && client.ws.readyState === WebSocket.OPEN) {
        client.ws.send(JSON.stringify({
          type: 'broadcast',
          channel,
          data,
          timestamp: Date.now()
        }));
      }
    });
  }

  // Get connected clients count
  getConnectedClientsCount() {
    return this.clients.size;
  }

  // Get clients by channel
  getClientsByChannel(channel) {
    const clients = [];
    this.clients.forEach((client) => {
      if (client.subscriptions.has(channel)) {
        clients.push({
          username: client.username,
          role: client.role,
          lastSeen: client.lastSeen
        });
      }
    });
    return clients;
  }
}

module.exports = new WebSocketService();
