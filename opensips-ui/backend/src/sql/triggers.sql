-- Database triggers for real-time event detection
-- This creates a change log table and triggers to track location changes

-- Create change log table
CREATE TABLE IF NOT EXISTS location_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(64) NOT NULL,
    domain VARCHAR(64) NOT NULL,
    action ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    contact <PERSON><PERSON><PERSON><PERSON>(255),
    user_agent <PERSON><PERSON>HA<PERSON>(255),
    expires INT,
    received VARCHAR(128),
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_changed_at (changed_at),
    INDEX idx_username_domain (username, domain)
);

-- Trigger for INSERT (new registration)
DELIMITER $$
CREATE TRIGGER location_after_insert 
AFTER INSERT ON location
FOR EACH ROW
BEGIN
    INSERT INTO location_changes (
        username, domain, action, contact, user_agent, expires, received
    ) VALUES (
        NEW.username, NEW.domain, 'INSERT', 
        NEW.contact, NEW.user_agent, NEW.expires, NEW.received
    );
END$$

-- Trigger for UPDATE (registration refresh)
CREATE TRIGGER location_after_update
AFTER UPDATE ON location  
FOR EACH ROW
BEGIN
    INSERT INTO location_changes (
        username, domain, action, contact, user_agent, expires, received
    ) VALUES (
        NEW.username, NEW.domain, 'UPDATE',
        NEW.contact, NEW.user_agent, NEW.expires, NEW.received
    );
END$$

-- Trigger for DELETE (unregistration)
CREATE TRIGGER location_after_delete
AFTER DELETE ON location
FOR EACH ROW  
BEGIN
    INSERT INTO location_changes (
        username, domain, action, contact, user_agent, expires, received
    ) VALUES (
        OLD.username, OLD.domain, 'DELETE',
        OLD.contact, OLD.user_agent, OLD.expires, OLD.received
    );
END$$
DELIMITER ;

-- Create dialog changes table for call tracking
CREATE TABLE IF NOT EXISTS dialog_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    callid VARCHAR(255) NOT NULL,
    from_uri VARCHAR(255),
    to_uri VARCHAR(255),
    action ENUM('CREATE', 'UPDATE', 'DELETE') NOT NULL,
    state INT,
    start_time INT,
    timeout INT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_changed_at (changed_at),
    INDEX idx_callid (callid)
);

-- Trigger for dialog INSERT (call start)
DELIMITER $$
CREATE TRIGGER dialog_after_insert
AFTER INSERT ON dialog
FOR EACH ROW
BEGIN
    INSERT INTO dialog_changes (
        callid, from_uri, to_uri, action, state, start_time, timeout
    ) VALUES (
        NEW.callid, NEW.from_uri, NEW.to_uri, 'CREATE',
        NEW.state, NEW.start_time, NEW.timeout
    );
END$$

-- Trigger for dialog UPDATE (call state change)
CREATE TRIGGER dialog_after_update
AFTER UPDATE ON dialog
FOR EACH ROW
BEGIN
    INSERT INTO dialog_changes (
        callid, from_uri, to_uri, action, state, start_time, timeout
    ) VALUES (
        NEW.callid, NEW.from_uri, NEW.to_uri, 'UPDATE',
        NEW.state, NEW.start_time, NEW.timeout
    );
END$$

-- Trigger for dialog DELETE (call end)
CREATE TRIGGER dialog_after_delete
AFTER DELETE ON dialog
FOR EACH ROW
BEGIN
    INSERT INTO dialog_changes (
        callid, from_uri, to_uri, action, state, start_time, timeout
    ) VALUES (
        OLD.callid, OLD.from_uri, OLD.to_uri, 'DELETE',
        OLD.state, OLD.start_time, OLD.timeout
    );
END$$
DELIMITER ;

-- Create cleanup procedure to remove old change logs
DELIMITER $$
CREATE PROCEDURE CleanupChangeLogs()
BEGIN
    -- Keep only last 24 hours of changes
    DELETE FROM location_changes 
    WHERE changed_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
    
    DELETE FROM dialog_changes 
    WHERE changed_at < DATE_SUB(NOW(), INTERVAL 24 HOUR);
END$$
DELIMITER ;

-- Create event to run cleanup every hour
CREATE EVENT IF NOT EXISTS cleanup_change_logs
ON SCHEDULE EVERY 1 HOUR
DO CALL CleanupChangeLogs();
