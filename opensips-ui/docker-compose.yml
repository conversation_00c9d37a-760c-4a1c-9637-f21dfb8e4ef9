version: '3.8'

services:
  # Backend API Service
  opensips-ui-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: opensips-ui-backend
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - CORS_ORIGIN=http://***********:3000
      - DB_HOST=opensips_mysql
      - DB_PORT=3306
      - DB_USER=opensips
      - DB_PASSWORD=opsips_password
      - DB_NAME=opensips
    networks:
      - opensips-network
    depends_on:
      - opensips_mysql
    restart: unless-stopped
    volumes:
      - ./backend/src:/app/src:ro
    command: ["npm", "run", "start"]

  # Frontend Service
  opensips-ui-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=http://***********:3001
    container_name: opensips-ui-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://***********:3001
    networks:
      - opensips-network
    depends_on:
      - opensips-ui-backend
    restart: unless-stopped

  # MySQL Database (reuse existing)
  opensips_mysql:
    image: mysql:8.0
    container_name: opensips_mysql
    environment:
      MYSQL_ROOT_PASSWORD: opsips_root_password
      MYSQL_DATABASE: opensips
      MYSQL_USER: opensips
      MYSQL_PASSWORD: opsips_password
    ports:
      - "3306:3306"
    volumes:
      - opensips_mysql_data:/var/lib/mysql
      - ./backend/database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - opensips-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

networks:
  opensips-network:
    external: true

volumes:
  opensips_mysql_data:
    external: true
