# API Reference

## 🌐 REST API Endpoints

### Base URLs
- **Backend API**: `http://localhost:3001/api`
- **Event Listener**: `http://localhost:3002/events`
- **WebSocket**: `ws://localhost:3001`

## 👥 User Management

### GET /api/users
Get all registered users with their status.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "username": "1001",
      "domain": "example.com",
      "email_address": "<EMAIL>",
      "contact": "sip:1001@*************:5060",
      "status": "online",
      "expires_in_seconds": 3456,
      "last_seen_formatted": "2025-06-16 00:45:30",
      "user_agent": "SIP Client v1.0",
      "ip_address": "*************"
    }
  ]
}
```

### GET /api/users/:username
Get specific user details.

**Parameters:**
- `username` (string): Username to query

**Response:**
```json
{
  "success": true,
  "data": {
    "username": "1001",
    "domain": "example.com",
    "status": "online",
    "expires_in_seconds": 3456,
    "contact": "sip:1001@*************:5060"
  }
}
```

## 📊 Statistics

### GET /api/stats
Get system statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "activeRegistrations": 5,
    "activeDialogs": 2,
    "callsToday": 15,
    "callsLastHour": 3,
    "totalUsers": 10,
    "failedCallsToday": 1,
    "successRate": 93.33,
    "avgCallDuration": 125,
    "callsThisMonth": 450,
    "registrationsToday": 8,
    "timestamp": "2025-06-16T00:45:30.123Z"
  }
}
```

### GET /api/stats/calls
Get detailed call statistics.

**Query Parameters:**
- `period` (string): `today`, `week`, `month` (default: `today`)
- `limit` (number): Maximum records to return (default: 100)

**Response:**
```json
{
  "success": true,
  "data": {
    "totalCalls": 15,
    "successfulCalls": 14,
    "failedCalls": 1,
    "avgDuration": 125,
    "calls": [
      {
        "callid": "abc123",
        "caller": "<EMAIL>",
        "callee": "<EMAIL>",
        "start_time": "2025-06-16T00:30:00Z",
        "end_time": "2025-06-16T00:32:05Z",
        "duration": 125,
        "status": "completed"
      }
    ]
  }
}
```

## 🔧 OpenSIPS MI Interface

### POST /api/mi/:command
Execute OpenSIPS MI command.

**Parameters:**
- `command` (string): MI command name

**Request Body:**
```json
{
  "params": ["param1", "param2"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "result": "command output"
  }
}
```

### Common MI Commands

#### GET /api/mi/uptime
Get OpenSIPS uptime.

**Response:**
```json
{
  "success": true,
  "data": {
    "uptime": "2 days, 5 hours, 30 minutes"
  }
}
```

#### GET /api/mi/users
Get registered users via MI interface.

**Response:**
```json
{
  "success": true,
  "data": {
    "users": {
      "Domains": [
        {
          "name": "example.com",
          "AORs": [
            {
              "AOR": "<EMAIL>",
              "Contacts": [
                {
                  "Contact": "sip:1001@*************:5060",
                  "Expires": 3456,
                  "UserAgent": "SIP Client v1.0"
                }
              ]
            }
          ]
        }
      ]
    }
  }
}
```

#### GET /api/mi/status
Get OpenSIPS system status.

**Response:**
```json
{
  "success": true,
  "data": {
    "version": "OpenSIPS 3.6.0",
    "processes": 8,
    "memory": "45.2 MB",
    "uptime": "2 days, 5 hours"
  }
}
```

## 📡 Event Listener API

### POST /events/contact_insert
Handle user registration event from OpenSIPS.

**Request Body:**
```json
{
  "domain": "example.com",
  "aor": "<EMAIL>",
  "uri": "sip:1001@*************:5060",
  "expires": "3600",
  "user_agent": "SIP Client v1.0",
  "received": "*************",
  "socket": "udp:***********:5060"
}
```

**Response:**
```json
{
  "success": true
}
```

### POST /events/contact_delete
Handle user unregistration event from OpenSIPS.

**Request Body:**
```json
{
  "domain": "example.com",
  "aor": "<EMAIL>",
  "uri": "sip:1001@*************:5060"
}
```

### POST /events/contact_update
Handle user re-registration event from OpenSIPS.

**Request Body:**
```json
{
  "domain": "example.com",
  "aor": "<EMAIL>",
  "uri": "sip:1001@*************:5060",
  "expires": "3600",
  "user_agent": "SIP Client v1.0"
}
```

### GET /events/health
Health check for event listener service.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-16T00:45:30.123Z",
  "uptime": 3600
}
```

## 🌐 WebSocket Events

### Connection
```javascript
const socket = io('http://localhost:3001');

socket.on('connect', () => {
  console.log('Connected to WebSocket');
  socket.emit('subscribe', 'users');
});
```

### User Events

#### user:status
User status change event.

**Payload:**
```json
{
  "username": "1001",
  "domain": "example.com",
  "status": "online",
  "expires_in_seconds": 3456,
  "last_seen": "2025-06-16T00:45:30Z",
  "user_agent": "SIP Client v1.0",
  "ip_address": "*************",
  "timestamp": "2025-06-16T00:45:30.123Z"
}
```

#### contact:inserted
Real-time user registration event.

**Payload:**
```json
{
  "username": "1001",
  "domain": "example.com",
  "aor": "<EMAIL>",
  "contact": "sip:1001@*************:5060",
  "status": "online",
  "expires_in": 3600,
  "user_agent": "SIP Client v1.0",
  "ip_address": "*************",
  "timestamp": "2025-06-16T00:45:30.123Z",
  "event_type": "registration"
}
```

#### contact:deleted
Real-time user unregistration event.

**Payload:**
```json
{
  "username": "1001",
  "domain": "example.com",
  "aor": "<EMAIL>",
  "contact": "sip:1001@*************:5060",
  "status": "offline",
  "timestamp": "2025-06-16T00:45:30.123Z",
  "event_type": "unregistration"
}
```

#### contact:updated
Real-time user re-registration event.

**Payload:**
```json
{
  "username": "1001",
  "domain": "example.com",
  "aor": "<EMAIL>",
  "contact": "sip:1001@*************:5060",
  "status": "online",
  "expires_in": 3600,
  "user_agent": "SIP Client v1.0",
  "ip_address": "*************",
  "timestamp": "2025-06-16T00:45:30.123Z",
  "event_type": "re-registration"
}
```

### Statistics Events

#### stats:update
Real-time statistics update.

**Payload:**
```json
{
  "activeRegistrations": 5,
  "activeDialogs": 2,
  "callsToday": 15,
  "totalUsers": 10,
  "timestamp": "2025-06-16T00:45:30.123Z"
}
```

## 🔐 Authentication

### POST /api/auth/login
User authentication.

**Request Body:**
```json
{
  "username": "admin",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "jwt_token_here",
    "user": {
      "username": "admin",
      "role": "administrator"
    }
  }
}
```

### Authorization Header
Include JWT token in requests:
```
Authorization: Bearer jwt_token_here
```

## ❌ Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": "Additional error details"
  }
}
```

### Common Error Codes
- `INVALID_REQUEST` - Malformed request
- `UNAUTHORIZED` - Authentication required
- `FORBIDDEN` - Insufficient permissions
- `NOT_FOUND` - Resource not found
- `DATABASE_ERROR` - Database connection issue
- `MI_ERROR` - OpenSIPS MI interface error
- `INTERNAL_ERROR` - Server error

## 📊 Rate Limiting

### Limits
- **API requests**: 100 requests/minute per IP
- **WebSocket connections**: 10 connections per IP
- **Event submissions**: 1000 events/minute

### Headers
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```
