# OpenSIPS UI - Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+ (for development)
- MySQL 8.0+
- OpenSIPS 3.6+

### 1. Clone Repository
```bash
git clone <repository-url>
cd opensips-ui
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

### 3. Deploy with Docker Compose
```bash
# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### 4. Access Services
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **Event Listener**: http://localhost:3002
- **OpenSIPS Control Panel**: http://localhost:8081

## 🏗️ Architecture Overview

### Service Components
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   OpenSIPS      │
│   (React)       │◄──►│   (Node.js)     │◄──►│   Server        │
│   Port: 3000    │    │   Port: 3001    │    │   Port: 5060    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         │              │ Event Listener  │              │
         └──────────────│   Port: 3002    │──────────────┘
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │   MySQL DB      │
                        │   Port: 3307    │
                        └─────────────────┘
```

### Real-time Monitoring System
- **Primary**: Database polling (2s intervals)
- **Secondary**: Event-driven (instant notifications)
- **Tertiary**: MI commands (advanced operations)

## 🔧 Configuration

### Environment Variables
```bash
# Database Configuration
DB_HOST=opensips_mysql
DB_USER=opensips
DB_PASSWORD=opensips_password
DB_NAME=opensips

# OpenSIPS MI Interface
OPENSIPS_MI_HOST=opensips_server
OPENSIPS_MI_PORT=8080

# CORS Configuration
CORS_ORIGIN=http://localhost:3000

# Node Environment
NODE_ENV=production
```

### Docker Network
All services run in `opensips_network` for internal communication.

## 📊 Monitoring Intervals

### Optimized Performance Settings
- **User monitoring**: 2s (5x faster than default)
- **Stats monitoring**: 3s (3.3x faster than default)
- **Event processing**: < 100ms (instant)
- **MI commands**: < 10ms response time

## 🔍 Health Checks

### Service Health Endpoints
```bash
# Backend health
curl http://localhost:3001/health

# Event listener health
curl http://localhost:3002/events/health

# Database connection
curl http://localhost:3001/api/stats
```

### Container Status
```bash
# Check all containers
docker ps

# Check specific service logs
docker logs opensips_ui_backend
docker logs opensips_ui_frontend
docker logs opensips_server
```

## 🚨 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check MySQL container
docker logs opensips_mysql

# Verify network connectivity
docker exec opensips_ui_backend ping opensips_mysql
```

#### 2. OpenSIPS MI Interface Not Responding
```bash
# Test MI directly
curl -X POST http://localhost:8080/mi \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc": "2.0", "method": "uptime", "id": "1"}'

# Check OpenSIPS logs
docker logs opensips_server
```

#### 3. Frontend Not Loading
```bash
# Check frontend logs
docker logs opensips_ui_frontend

# Verify CORS configuration
curl -H "Origin: http://localhost:3000" http://localhost:3001/health
```

### Performance Optimization

#### Database Tuning
```sql
-- Optimize location table
ALTER TABLE location ADD INDEX idx_expires (expires);
ALTER TABLE location ADD INDEX idx_username_domain (username, domain);
```

#### Memory Usage
```bash
# Monitor container memory
docker stats

# Adjust container limits in docker-compose.yml
```

## 🔐 Security

### Production Deployment
- Change default passwords
- Enable SSL/TLS certificates
- Configure firewall rules
- Set up monitoring alerts

### Network Security
```bash
# Restrict external access
iptables -A INPUT -p tcp --dport 3001 -s trusted_ip -j ACCEPT
iptables -A INPUT -p tcp --dport 3002 -s trusted_ip -j ACCEPT
```

## 📈 Scaling

### Horizontal Scaling
- Load balancer for frontend
- Multiple backend instances
- Database clustering
- Redis for session management

### Monitoring & Alerts
- Prometheus metrics
- Grafana dashboards
- AlertManager notifications
- Log aggregation with ELK stack

## 📋 Docker Compose Services

### Service Overview
```yaml
version: '3.8'
services:
  # Frontend React Application
  opensips_ui_frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_BACKEND_URL=http://localhost:3001

  # Backend Node.js API
  opensips_ui_backend:
    build: ./backend
    ports:
      - "3001:3001"  # API server
      - "3002:3002"  # Event listener
    environment:
      - DB_HOST=opensips_mysql
      - OPENSIPS_MI_HOST=opensips_server
      - CORS_ORIGIN=http://localhost:3000
    depends_on:
      - opensips_mysql

  # MySQL Database
  opensips_mysql:
    image: mysql:8.0
    ports:
      - "3307:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=opensips
      - MYSQL_USER=opensips
      - MYSQL_PASSWORD=opensips_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql

  # OpenSIPS Server
  opensips_server:
    image: opensips/opensips:3.6
    ports:
      - "5060:5060/udp"  # SIP
      - "8080:8080"      # MI HTTP
    volumes:
      - ./opensips/opensips.cfg:/usr/local/etc/opensips/opensips.cfg
    depends_on:
      - opensips_mysql

networks:
  default:
    name: opensips_network

volumes:
  mysql_data:
```

### Environment Configuration
Create `.env` file:
```bash
# Database Configuration
DB_HOST=opensips_mysql
DB_PORT=3306
DB_USER=opensips
DB_PASSWORD=opensips_password
DB_NAME=opensips

# OpenSIPS MI Configuration
OPENSIPS_MI_HOST=opensips_server
OPENSIPS_MI_PORT=8080

# Application Configuration
NODE_ENV=production
CORS_ORIGIN=http://localhost:3000

# MySQL Root Configuration
MYSQL_ROOT_PASSWORD=root_password
MYSQL_DATABASE=opensips
MYSQL_USER=opensips
MYSQL_PASSWORD=opensips_password
```

### Production Deployment Commands
```bash
# Build and start all services
docker-compose up -d --build

# Scale backend for high availability
docker-compose up -d --scale opensips_ui_backend=3

# Update specific service
docker-compose up -d --no-deps opensips_ui_backend

# View logs
docker-compose logs -f opensips_ui_backend

# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v
```
