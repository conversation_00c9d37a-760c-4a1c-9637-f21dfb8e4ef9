# OpenSIPS UI - Development Guide

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- Docker & Docker Compose
- Git
- VS Code (recommended)

### 1. Local Development Environment
```bash
# Clone repository
git clone <repository-url>
cd opensips-ui

# Install dependencies
cd frontend && npm install
cd ../backend && npm install

# Start development services
docker-compose -f docker-compose.dev.yml up -d
```

### 2. Development Workflow
```bash
# Frontend development (hot reload)
cd frontend
npm run dev

# Backend development (nodemon)
cd backend
npm run dev

# Run tests
npm test
```

## 🏗️ Architecture Deep Dive

### Real-time Monitoring System

#### 1. Hybrid Monitoring Strategy
```javascript
// Primary: Database Polling (Reliable)
setInterval(async () => {
  const users = await getUsersFromDatabase();
  detectChanges(users);
}, 2000); // 2s intervals

// Secondary: Event-driven (Instant)
app.post('/events/contact_insert', async (req, res) => {
  await processContactInsert(req.body);
  io.emit('contact:inserted', eventData);
});

// Tertiary: MI Commands (Advanced)
const miResult = await miService.executeCommand('ul_dump');
```

#### 2. WebSocket Real-time Updates
```javascript
// Backend: Event emission
io.to('users').emit('user:status', {
  username: 'user1',
  status: 'online',
  timestamp: new Date().toISOString()
});

// Frontend: Event handling
socket.on('user:status', (data) => {
  updateUserStatus(data);
  showNotification(`${data.username} is ${data.status}`);
});
```

### Event-Driven Architecture

#### OpenSIPS Event Flow
```
OpenSIPS Events → Event Listener → Database Lookup → WebSocket Push → Frontend Update
     ↓               ↓                    ↓              ↓              ↓
E_UL_CONTACT_*  → Port 3002 →    MySQL Query  →   Socket.IO  →   React State
```

#### Event Types Supported
- `E_UL_CONTACT_INSERT` - User registration
- `E_UL_CONTACT_DELETE` - User unregistration  
- `E_UL_CONTACT_UPDATE` - User re-registration
- `E_USER_REGISTERED` - Legacy support
- `E_CALL_STARTED` - Call initiation
- `E_CALL_ENDED` - Call termination

## 🔧 Backend Development

### Service Architecture
```
src/
├── config/
│   ├── database.js      # MySQL connection
│   └── redis.js         # Redis cache
├── services/
│   ├── realtime.js      # Real-time monitoring
│   ├── eventListener.js # Event processing
│   ├── mi.js           # OpenSIPS MI interface
│   └── auth.js         # Authentication
├── routes/
│   ├── users.js        # User management
│   ├── stats.js        # Statistics
│   └── mi.js           # MI commands
└── server.js           # Main application
```

### Adding New Event Types
```javascript
// 1. Add route in eventListener.js
this.app.post('/events/new_event', this.handleNewEvent.bind(this));

// 2. Add handler method
async handleNewEvent(req, res) {
  await this.processNewEvent(req.body);
  res.json({ success: true });
}

// 3. Add processing logic
async processNewEvent(data) {
  const params = this.parseEventData(data);
  
  // Emit real-time event
  this.io.to('events').emit('new:event', {
    type: 'new_event',
    data: params,
    timestamp: new Date().toISOString()
  });
}
```

### Database Optimization
```javascript
// Efficient user monitoring query
const optimizedQuery = `
  SELECT 
    s.username,
    s.domain,
    l.contact,
    l.expires,
    l.user_agent,
    CASE 
      WHEN l.expires > UNIX_TIMESTAMP() THEN 'online'
      ELSE 'offline'
    END as status,
    TIMESTAMPDIFF(SECOND, NOW(), FROM_UNIXTIME(l.expires)) as expires_in
  FROM subscriber s
  LEFT JOIN location l ON s.username = l.username AND s.domain = l.domain
  WHERE l.expires > UNIX_TIMESTAMP() - 300
  ORDER BY l.last_modified DESC
`;
```

## 🎨 Frontend Development

### Component Structure
```
src/
├── components/
│   ├── Dashboard/       # Main dashboard
│   ├── Users/          # User management
│   ├── Stats/          # Statistics display
│   └── Common/         # Shared components
├── hooks/
│   ├── useSocket.js    # WebSocket hook
│   ├── useUsers.js     # User data hook
│   └── useStats.js     # Statistics hook
├── services/
│   ├── api.js          # API client
│   ├── socket.js       # Socket.IO client
│   └── auth.js         # Authentication
└── utils/
    ├── formatters.js   # Data formatting
    └── constants.js    # App constants
```

### Real-time Data Hooks
```javascript
// useSocket.js - WebSocket management
export const useSocket = () => {
  const [socket, setSocket] = useState(null);
  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    const newSocket = io(process.env.REACT_APP_BACKEND_URL);
    
    newSocket.on('connect', () => {
      setIsConnected(true);
      newSocket.emit('subscribe', 'users');
    });

    setSocket(newSocket);
    return () => newSocket.close();
  }, []);

  return { socket, isConnected };
};

// useUsers.js - User data management
export const useUsers = () => {
  const [users, setUsers] = useState([]);
  const { socket } = useSocket();

  useEffect(() => {
    if (!socket) return;

    socket.on('user:status', (userData) => {
      setUsers(prev => updateUserInList(prev, userData));
    });

    socket.on('contact:inserted', (contactData) => {
      setUsers(prev => addUserToList(prev, contactData));
      showNotification(`${contactData.username} registered`);
    });

    return () => {
      socket.off('user:status');
      socket.off('contact:inserted');
    };
  }, [socket]);

  return { users };
};
```

### Performance Optimization
```javascript
// Memoized components for better performance
const UserList = memo(({ users }) => {
  return (
    <div>
      {users.map(user => (
        <UserCard key={`${user.username}@${user.domain}`} user={user} />
      ))}
    </div>
  );
});

// Debounced search
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => clearTimeout(handler);
  }, [value, delay]);

  return debouncedValue;
};
```

## 🧪 Testing

### Backend Testing
```javascript
// Test event processing
describe('Event Listener Service', () => {
  test('should process contact insert event', async () => {
    const eventData = {
      domain: 'test.com',
      aor: '<EMAIL>',
      uri: 'sip:user1@*************:5060'
    };

    const response = await request(app)
      .post('/events/contact_insert')
      .send(eventData)
      .expect(200);

    expect(response.body.success).toBe(true);
  });
});

// Test MI service
describe('MI Service', () => {
  test('should execute MI command', async () => {
    const result = await miService.executeCommand('uptime');
    expect(result).toHaveProperty('uptime');
  });
});
```

### Frontend Testing
```javascript
// Test WebSocket integration
describe('useSocket hook', () => {
  test('should connect to WebSocket', () => {
    const { result } = renderHook(() => useSocket());
    
    act(() => {
      // Simulate connection
      mockSocket.emit('connect');
    });

    expect(result.current.isConnected).toBe(true);
  });
});

// Test user updates
describe('User Management', () => {
  test('should update user status', () => {
    const { result } = renderHook(() => useUsers());
    
    act(() => {
      mockSocket.emit('user:status', {
        username: 'test',
        status: 'online'
      });
    });

    expect(result.current.users).toContainEqual(
      expect.objectContaining({ username: 'test', status: 'online' })
    );
  });
});
```

## 🔍 Debugging

### Backend Debugging
```javascript
// Enable debug logging
DEBUG=opensips-ui:* npm run dev

// Monitor real-time events
docker logs opensips_ui_backend -f | grep "📡\|👤\|📊"

// Test MI interface directly
curl -X POST http://localhost:8080/mi \
  -H 'Content-Type: application/json' \
  -d '{"jsonrpc": "2.0", "method": "ps", "id": "1"}'
```

### Frontend Debugging
```javascript
// WebSocket debugging
socket.on('connect', () => console.log('Connected to WebSocket'));
socket.on('disconnect', () => console.log('Disconnected from WebSocket'));
socket.onAny((event, ...args) => console.log('Event:', event, args));

// Performance monitoring
import { Profiler } from 'react';

<Profiler id="UserList" onRender={onRenderCallback}>
  <UserList users={users} />
</Profiler>
```

## 📚 API Documentation

### REST Endpoints
- `GET /api/users` - Get all users
- `GET /api/stats` - Get system statistics
- `POST /api/mi/:command` - Execute MI command
- `GET /health` - Health check

### WebSocket Events
- `user:status` - User status change
- `contact:inserted` - User registration
- `contact:deleted` - User unregistration
- `stats:update` - Statistics update

### Event Listener Endpoints
- `POST /events/contact_insert` - Contact insertion
- `POST /events/contact_delete` - Contact deletion
- `POST /events/opensips` - Generic OpenSIPS event
