# OpenSIPS Configuration for Real-time Events

## 🎯 Overview

This guide explains how to configure OpenSIPS to send real-time events to the OpenSIPS UI backend for instant user registration/unregistration notifications.

## 📋 Prerequisites

- OpenSIPS 3.6+ with event support
- `rest_client` module available
- `json` module available
- Network connectivity to backend service

## 🔧 Module Configuration

### Required Modules
Add these modules to your `opensips.cfg`:

```bash
# Load required modules
loadmodule "rest_client.so"
loadmodule "json.so"
loadmodule "usrloc.so"

# Rest client parameters
modparam("rest_client", "connection_timeout", 3)
modparam("rest_client", "max_async_transfers", 100)
modparam("rest_client", "ssl_verifypeer", 0)
modparam("rest_client", "ssl_verifyhost", 0)

# Usrloc parameters (if not already configured)
modparam("usrloc", "nat_bflag", "NAT")
modparam("usrloc", "working_mode_preset", "single-instance-no-db")
```

## 📡 Event Routes Implementation

### 1. Contact Insert Event (Registration)
```bash
# Handle user registration events
event_route[E_UL_CONTACT_INSERT] {
    xlog("L_INFO", "📡 Contact insert event triggered for $param(aor)\n");
    
    # Build JSON payload with contact information
    $json(contact/domain) = $param(domain);
    $json(contact/aor) = $param(aor);
    $json(contact/uri) = $param(uri);
    $json(contact/expires) = $param(expires);
    $json(contact/user_agent) = $param(user_agent);
    $json(contact/received) = $param(received);
    $json(contact/socket) = $param(socket);
    $json(contact/timestamp) = $Ts;
    
    # Send event to backend (async)
    async(
        rest_post("http://opensips_ui_backend:3002/events/contact_insert", 
                  "$json(contact)", "$var(result)"),
        CONTACT_INSERT_RESUME
    );
}

# Resume route for contact insert
route[CONTACT_INSERT_RESUME] {
    if ($rc < 0) {
        xlog("L_WARN", "❌ Failed to send contact insert event for $param(aor): $var(result)\n");
    } else {
        xlog("L_INFO", "✅ Contact insert event sent successfully for $param(aor)\n");
    }
}
```

### 2. Contact Delete Event (Unregistration)
```bash
# Handle user unregistration events
event_route[E_UL_CONTACT_DELETE] {
    xlog("L_INFO", "📡 Contact delete event triggered for $param(aor)\n");
    
    # Build JSON payload
    $json(contact/domain) = $param(domain);
    $json(contact/aor) = $param(aor);
    $json(contact/uri) = $param(uri);
    $json(contact/timestamp) = $Ts;
    
    # Send event to backend (async)
    async(
        rest_post("http://opensips_ui_backend:3002/events/contact_delete", 
                  "$json(contact)", "$var(result)"),
        CONTACT_DELETE_RESUME
    );
}

# Resume route for contact delete
route[CONTACT_DELETE_RESUME] {
    if ($rc < 0) {
        xlog("L_WARN", "❌ Failed to send contact delete event for $param(aor): $var(result)\n");
    } else {
        xlog("L_INFO", "✅ Contact delete event sent successfully for $param(aor)\n");
    }
}
```

### 3. Contact Update Event (Re-registration)
```bash
# Handle user re-registration events
event_route[E_UL_CONTACT_UPDATE] {
    xlog("L_INFO", "📡 Contact update event triggered for $param(aor)\n");
    
    # Build JSON payload
    $json(contact/domain) = $param(domain);
    $json(contact/aor) = $param(aor);
    $json(contact/uri) = $param(uri);
    $json(contact/expires) = $param(expires);
    $json(contact/user_agent) = $param(user_agent);
    $json(contact/received) = $param(received);
    $json(contact/timestamp) = $Ts;
    
    # Send event to backend (async)
    async(
        rest_post("http://opensips_ui_backend:3002/events/contact_update", 
                  "$json(contact)", "$var(result)"),
        CONTACT_UPDATE_RESUME
    );
}

# Resume route for contact update
route[CONTACT_UPDATE_RESUME] {
    if ($rc < 0) {
        xlog("L_WARN", "❌ Failed to send contact update event for $param(aor): $var(result)\n");
    } else {
        xlog("L_INFO", "✅ Contact update event sent successfully for $param(aor)\n");
    }
}
```

## 🔄 Call Events (Optional)

### Dialog Events for Call Monitoring
```bash
# Load dialog module for call events
loadmodule "dialog.so"
modparam("dialog", "dlg_flag", 4)
modparam("dialog", "profiles_with_value", "caller")

# Call start event
event_route[E_DLG_STATE_CHANGED] {
    if ($param(new_state) == 4) { # CONFIRMED state
        xlog("L_INFO", "📞 Call started: $param(callid)\n");
        
        $json(call/callid) = $param(callid);
        $json(call/from_uri) = $param(from_uri);
        $json(call/to_uri) = $param(to_uri);
        $json(call/state) = "started";
        $json(call/timestamp) = $Ts;
        
        async(
            rest_post("http://opensips_ui_backend:3002/events/call_started", 
                      "$json(call)", "$var(result)"),
            CALL_EVENT_RESUME
        );
    } else if ($param(new_state) == 5) { # DELETED state
        xlog("L_INFO", "📞 Call ended: $param(callid)\n");
        
        $json(call/callid) = $param(callid);
        $json(call/duration) = $param(duration);
        $json(call/state) = "ended";
        $json(call/timestamp) = $Ts;
        
        async(
            rest_post("http://opensips_ui_backend:3002/events/call_ended", 
                      "$json(call)", "$var(result)"),
            CALL_EVENT_RESUME
        );
    }
}

# Resume route for call events
route[CALL_EVENT_RESUME] {
    if ($rc < 0) {
        xlog("L_WARN", "❌ Failed to send call event: $var(result)\n");
    } else {
        xlog("L_INFO", "✅ Call event sent successfully\n");
    }
}
```

## 🛠️ Configuration Testing

### 1. Test Event Routes
```bash
# Test configuration syntax
opensips -c -f /path/to/opensips.cfg

# Check if modules are loaded
opensips -M

# Test REST client connectivity
opensips-cli -x mi rest_get http://opensips_ui_backend:3002/events/health
```

### 2. Manual Event Testing
```bash
# Simulate contact insert event
curl -X POST http://localhost:3002/events/contact_insert \
  -H 'Content-Type: application/json' \
  -d '{
    "domain": "example.com",
    "aor": "<EMAIL>",
    "uri": "sip:test@*************:5060",
    "expires": "3600",
    "user_agent": "Test Client"
  }'

# Check backend logs
docker logs opensips_ui_backend | grep "contact"
```

### 3. Monitor Event Flow
```bash
# Watch OpenSIPS logs for events
tail -f /var/log/opensips.log | grep "📡\|✅\|❌"

# Monitor backend event processing
docker logs opensips_ui_backend -f | grep "👤\|📡"
```

## 🔧 Troubleshooting

### Common Issues

#### 1. REST Client Module Not Found
```bash
# Check if rest_client module is available
find /usr -name "*rest_client*" 2>/dev/null

# Install missing modules (Ubuntu/Debian)
apt-get install opensips-restclient-modules
```

#### 2. Network Connectivity Issues
```bash
# Test connectivity from OpenSIPS container
docker exec opensips_server curl -v http://opensips_ui_backend:3002/events/health

# Check Docker network
docker network inspect opensips_network
```

#### 3. JSON Module Issues
```bash
# Verify JSON module is loaded
opensips-cli -x mi which

# Test JSON functionality
opensips-cli -x mi json_test
```

### Performance Optimization

#### 1. Async Event Processing
```bash
# Use async processing to avoid blocking
async(rest_post(...), RESUME_ROUTE);

# Set appropriate timeouts
modparam("rest_client", "connection_timeout", 3)
modparam("rest_client", "max_async_transfers", 100)
```

#### 2. Error Handling
```bash
# Add error handling in resume routes
route[CONTACT_INSERT_RESUME] {
    if ($rc < 0) {
        # Log error but don't fail the registration
        xlog("L_WARN", "Event notification failed, but registration continues\n");
    }
}
```

## 📊 Monitoring & Logging

### Event Statistics
```bash
# Add statistics tracking
loadmodule "statistics.so"

# Define custom statistics
modparam("statistics", "variable", "events_sent")
modparam("statistics", "variable", "events_failed")

# Update statistics in event routes
route[CONTACT_INSERT_RESUME] {
    if ($rc < 0) {
        update_stat("events_failed", "+1");
    } else {
        update_stat("events_sent", "+1");
    }
}

# Query statistics via MI
opensips-cli -x mi get_statistics events_sent events_failed
```

### Log Configuration
```bash
# Configure detailed logging for events
log_level = 3
log_stderror = no
log_facility = LOG_LOCAL0

# Custom log format for events
xlog("L_INFO", "EVENT|$Ts|$param(aor)|$param(uri)|$var(result)\n");
```

## 🚀 Production Deployment

### Security Considerations
```bash
# Use HTTPS for production
rest_post("https://backend.example.com:3002/events/contact_insert", ...)

# Add authentication headers
$var(auth_header) = "Authorization: Bearer " + $var(api_token);
rest_post(..., "$json(contact)", "$var(result)", "$var(auth_header)");
```

### High Availability
```bash
# Multiple backend endpoints for failover
if (!rest_post("http://backend1:3002/events/contact_insert", ...)) {
    rest_post("http://backend2:3002/events/contact_insert", ...);
}
```

### Rate Limiting
```bash
# Implement rate limiting for events
loadmodule "ratelimit.so"
modparam("ratelimit", "timer_interval", 10)

# Check rate limit before sending events
if (!rl_check("events", "100")) {
    xlog("L_WARN", "Event rate limit exceeded\n");
    exit;
}
```
