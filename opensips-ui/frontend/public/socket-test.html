<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Socket.IO Connection Test</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        #logs {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Socket.IO Connection Test</h1>
    
    <div id="status" class="status disconnected">
        Status: Disconnected
    </div>
    
    <div>
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
        <button onclick="clearLogs()">Clear Logs</button>
        <button onclick="testStats()">Test Stats</button>
    </div>
    
    <h3>Connection Logs:</h3>
    <div id="logs"></div>
    
    <script>
        let socket = null;
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
        }
        
        function updateStatus(status, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = `Status: ${status}`;
            statusEl.className = `status ${className}`;
        }
        
        function connect() {
            if (socket && socket.connected) {
                log('Already connected');
                return;
            }
            
            log('Attempting to connect to Socket.IO server...');
            updateStatus('Connecting...', 'connecting');
            
            socket = io('http://localhost:3001', {
                transports: ['polling', 'websocket'],
                timeout: 10000,
                forceNew: true,
                autoConnect: true,
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            });
            
            socket.on('connect', () => {
                log('✅ Connected to Socket.IO server');
                log(`Socket ID: ${socket.id}`);
                updateStatus('Connected', 'connected');
            });
            
            socket.on('connect_error', (error) => {
                log(`❌ Connection error: ${error.message}`);
                updateStatus('Connection Error', 'disconnected');
            });
            
            socket.on('disconnect', (reason) => {
                log(`🔌 Disconnected: ${reason}`);
                updateStatus('Disconnected', 'disconnected');
            });
            
            socket.on('welcome', (data) => {
                log(`👋 Welcome message: ${data.message}`);
            });
            
            socket.on('stats-update', (data) => {
                log(`📊 Stats update: ${JSON.stringify(data)}`);
            });
            
            socket.on('error', (error) => {
                log(`❌ Socket error: ${error}`);
            });
        }
        
        function disconnect() {
            if (socket) {
                log('Disconnecting...');
                socket.disconnect();
                socket = null;
                updateStatus('Disconnected', 'disconnected');
            }
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function testStats() {
            if (socket && socket.connected) {
                log('📊 Subscribing to stats...');
                socket.emit('subscribe-stats');
            } else {
                log('❌ Not connected to server');
            }
        }
        
        // Auto-connect on page load
        window.onload = function() {
            log('Page loaded, starting connection test...');
            connect();
        };
    </script>
</body>
</html>
