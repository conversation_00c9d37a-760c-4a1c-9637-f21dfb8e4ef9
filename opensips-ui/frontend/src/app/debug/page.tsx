'use client';

import { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useSocket } from '@/hooks/useSocket';
import { useRealtimeStats } from '@/hooks/useRealtimeStats';
import { useRealtimeCalls } from '@/hooks/useRealtimeCalls';
import { useRealtimeUsers } from '@/hooks/useRealtimeUsers';

export default function DebugPage() {
  const [logs, setLogs] = useState<string[]>([]);
  
  // Socket connection
  const { isConnected, isConnecting, connectionError } = useSocket();
  const { stats, lastUpdate: statsLastUpdate, error: statsError } = useRealtimeStats();
  const { calls, lastUpdate: callsLastUpdate, error: callsError } = useRealtimeCalls();
  const { users, lastUpdate: usersLastUpdate, error: usersError } = useRealtimeUsers();

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
  };

  useEffect(() => {
    addLog(`Socket Status: Connected=${isConnected}, Connecting=${isConnecting}, Error=${connectionError}`);
  }, [isConnected, isConnecting, connectionError]);

  useEffect(() => {
    if (stats) {
      addLog(`Stats Update: ${JSON.stringify(stats)}`);
    }
    if (statsError) {
      addLog(`Stats Error: ${statsError}`);
    }
  }, [stats, statsError]);

  useEffect(() => {
    if (calls.length > 0) {
      addLog(`Calls Update: ${calls.length} calls received`);
    }
    if (callsError) {
      addLog(`Calls Error: ${callsError}`);
    }
  }, [calls, callsError]);

  useEffect(() => {
    if (users.length > 0) {
      addLog(`Users Update: ${users.length} users received`);
    }
    if (usersError) {
      addLog(`Users Error: ${usersError}`);
    }
  }, [users, usersError]);

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Socket.IO Debug Page</h1>
        <Button onClick={clearLogs}>Clear Logs</Button>
      </div>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Socket Connection</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? "Connected" : isConnecting ? "Connecting..." : "Disconnected"}
            </Badge>
            {connectionError && (
              <p className="text-xs text-red-500 mt-2">{connectionError}</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Stats</CardTitle>
          </CardHeader>
          <CardContent>
            {stats ? (
              <div className="space-y-1">
                <p className="text-xs">Active: {stats.activeRegistrations}</p>
                <p className="text-xs">Total: {stats.totalUsers}</p>
                <p className="text-xs">Calls: {stats.callsLastHour}</p>
                {statsLastUpdate && (
                  <p className="text-xs text-gray-500">
                    Updated: {new Date(statsLastUpdate).toLocaleTimeString()}
                  </p>
                )}
              </div>
            ) : (
              <Badge variant="secondary">No Data</Badge>
            )}
            {statsError && (
              <p className="text-xs text-red-500 mt-2">{statsError}</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Calls</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={calls.length > 0 ? "default" : "secondary"}>
              {calls.length} calls
            </Badge>
            {callsLastUpdate && (
              <p className="text-xs text-gray-500 mt-2">
                Updated: {new Date(callsLastUpdate).toLocaleTimeString()}
              </p>
            )}
            {callsError && (
              <p className="text-xs text-red-500 mt-2">{callsError}</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Users</CardTitle>
          </CardHeader>
          <CardContent>
            <Badge variant={users.length > 0 ? "default" : "secondary"}>
              {users.length} users
            </Badge>
            {usersLastUpdate && (
              <p className="text-xs text-gray-500 mt-2">
                Updated: {new Date(usersLastUpdate).toLocaleTimeString()}
              </p>
            )}
            {usersError && (
              <p className="text-xs text-red-500 mt-2">{usersError}</p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-md h-96 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <p className="text-gray-500">No logs yet...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Raw Data */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Raw Stats Data</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto">
              {JSON.stringify(stats, null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Raw Calls Data</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(calls.slice(0, 2), null, 2)}
            </pre>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Raw Users Data</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
              {JSON.stringify(users.slice(0, 2), null, 2)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
