'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  Server, 
  Database, 
  Cpu, 
  MemoryStick, 
  HardDrive,
  Network,
  Clock,
  AlertTriangle,
  CheckCircle,
  Wifi,
  WifiOff,
  RefreshCw
} from 'lucide-react';
import { useSocket, useRealtimeStats, useRealtimeEvents } from '@/hooks/useSocket';

interface SystemInfo {
  opensips: {
    version: string;
    uptime: string;
    processes: number;
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
  };
  database: {
    status: string;
    connections: number;
    queries: number;
    slowQueries: number;
  };
  system: {
    cpu: number;
    memory: number;
    disk: number;
    load: number[];
  };
}

interface Event {
  type: string;
  source: string;
  destination: string;
  method: string;
  sip_code: number;
  sip_reason: string;
  timestamp: number;
  event_time: string;
}

export default function MonitoringPage() {
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);

  // Real-time connection
  const { isConnected, isConnecting } = useSocket();
  const { stats, lastUpdate } = useRealtimeStats();
  const { events: realtimeEvents } = useRealtimeEvents();

  const fetchSystemInfo = async () => {
    try {
      // Mock system information
      const mockSystemInfo: SystemInfo = {
        opensips: {
          version: '3.4.0',
          uptime: '2 days, 5 hours, 23 minutes',
          processes: 8,
          memory: {
            used: 45,
            total: 128,
            percentage: 35
          }
        },
        database: {
          status: 'Connected',
          connections: 12,
          queries: 1547,
          slowQueries: 3
        },
        system: {
          cpu: 23,
          memory: 67,
          disk: 45,
          load: [0.8, 1.2, 0.9]
        }
      };

      // Mock events
      const mockEvents: Event[] = [
        {
          type: 'call',
          source: '1001',
          destination: '1002',
          method: 'INVITE',
          sip_code: 200,
          sip_reason: 'OK',
          timestamp: Date.now() / 1000 - 300,
          event_time: new Date(Date.now() - 300000).toISOString()
        },
        {
          type: 'registration',
          source: '1003',
          destination: 'localhost',
          method: 'REGISTER',
          sip_code: 200,
          sip_reason: 'OK',
          timestamp: Date.now() / 1000 - 600,
          event_time: new Date(Date.now() - 600000).toISOString()
        },
        {
          type: 'call',
          source: '1004',
          destination: '1001',
          method: 'INVITE',
          sip_code: 486,
          sip_reason: 'Busy Here',
          timestamp: Date.now() / 1000 - 900,
          event_time: new Date(Date.now() - 900000).toISOString()
        }
      ];

      setSystemInfo(mockSystemInfo);
      setEvents(mockEvents);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch system info:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemInfo();
  }, []);

  // Update events with real-time data
  useEffect(() => {
    if (realtimeEvents && realtimeEvents.length > 0) {
      setEvents(realtimeEvents);
    }
  }, [realtimeEvents]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'connected':
      case 'running':
      case 'online':
        return 'default';
      case 'warning':
        return 'secondary';
      case 'error':
      case 'offline':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'call':
        return <Activity className="h-4 w-4" />;
      case 'registration':
        return <CheckCircle className="h-4 w-4" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Activity className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading monitoring data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Monitoring</h1>
          <p className="text-muted-foreground">
            Real-time system health and performance metrics
          </p>
          {lastUpdate && (
            <p className="text-xs text-muted-foreground">
              Last update: {new Date(lastUpdate).toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {/* Connection Status */}
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? (
              <>
                <Wifi className="h-3 w-3 mr-1" />
                Real-time
              </>
            ) : isConnecting ? (
              <>
                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                <WifiOff className="h-3 w-3 mr-1" />
                Offline
              </>
            )}
          </Badge>
          <Button onClick={fetchSystemInfo} size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Real-time Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.activeRegistrations || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently registered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Calls</CardTitle>
            <Activity className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.activeDialogs || 0}</div>
            <p className="text-xs text-muted-foreground">
              Ongoing conversations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Calls Today</CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.callsToday || 0}</div>
            <p className="text-xs text-muted-foreground">
              Total calls made
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Hour</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.callsLastHour || 0}</div>
            <p className="text-xs text-muted-foreground">
              Recent activity
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Monitoring Tabs */}
      <Tabs defaultValue="system" className="space-y-4">
        <TabsList>
          <TabsTrigger value="system">System Health</TabsTrigger>
          <TabsTrigger value="opensips">OpenSIPS</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
        </TabsList>

        <TabsContent value="system" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
                <Cpu className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemInfo?.system.cpu}%</div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${systemInfo?.system.cpu}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Memory</CardTitle>
                <MemoryStick className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemInfo?.system.memory}%</div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ width: `${systemInfo?.system.memory}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Disk Usage</CardTitle>
                <HardDrive className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{systemInfo?.system.disk}%</div>
                <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                  <div 
                    className="bg-yellow-600 h-2 rounded-full" 
                    style={{ width: `${systemInfo?.system.disk}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Load Average</CardTitle>
                <Network className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {systemInfo?.system.load[0].toFixed(2)}
                </div>
                <p className="text-xs text-muted-foreground">
                  1m: {systemInfo?.system.load[0].toFixed(2)} | 
                  5m: {systemInfo?.system.load[1].toFixed(2)} | 
                  15m: {systemInfo?.system.load[2].toFixed(2)}
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="opensips" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Server className="h-5 w-5 mr-2" />
                  OpenSIPS Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Version:</span>
                  <span className="font-medium">{systemInfo?.opensips.version}</span>
                </div>
                <div className="flex justify-between">
                  <span>Uptime:</span>
                  <span className="font-medium">{systemInfo?.opensips.uptime}</span>
                </div>
                <div className="flex justify-between">
                  <span>Processes:</span>
                  <span className="font-medium">{systemInfo?.opensips.processes}</span>
                </div>
                <div className="flex justify-between">
                  <span>Status:</span>
                  <Badge variant={isConnected ? "default" : "destructive"}>
                    {isConnected ? "Running" : "Stopped"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MemoryStick className="h-5 w-5 mr-2" />
                  Memory Usage
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Used:</span>
                  <span className="font-medium">
                    {formatBytes(systemInfo?.opensips.memory.used * 1024 * 1024 || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Total:</span>
                  <span className="font-medium">
                    {formatBytes(systemInfo?.opensips.memory.total * 1024 * 1024 || 0)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Percentage:</span>
                  <span className="font-medium">{systemInfo?.opensips.memory.percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${systemInfo?.opensips.memory.percentage}%` }}
                  ></div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="database" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Database Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{systemInfo?.database.connections}</div>
                  <p className="text-sm text-muted-foreground">Active Connections</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{systemInfo?.database.queries}</div>
                  <p className="text-sm text-muted-foreground">Total Queries</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{systemInfo?.database.slowQueries}</div>
                  <p className="text-sm text-muted-foreground">Slow Queries</p>
                </div>
                <div className="text-center">
                  <Badge variant={getStatusColor(systemInfo?.database.status || '')}>
                    {systemInfo?.database.status}
                  </Badge>
                  <p className="text-sm text-muted-foreground mt-1">Connection Status</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>
                Real-time system events and activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {events.map((event, index) => (
                    <div key={index} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <div className="flex-shrink-0">
                        {getEventIcon(event.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {event.type}
                          </Badge>
                          <span className="text-sm font-medium">
                            {event.source} → {event.destination}
                          </span>
                          <Badge variant={event.sip_code === 200 ? "default" : "destructive"} className="text-xs">
                            {event.sip_code} {event.sip_reason}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {event.method} • {new Date(event.event_time).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
