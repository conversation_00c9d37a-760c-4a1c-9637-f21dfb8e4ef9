'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Activity, 
  Users, 
  Phone, 
  TrendingUp, 
  Clock, 
  Server, 
  Database,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api';
import { SystemOverview, Metrics, SystemHealth } from '@/types/monitoring';

export default function MonitoringPage() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(false);
  
  // System Overview State
  const [systemOverview, setSystemOverview] = useState<SystemOverview | null>(null);
  
  // Metrics State
  const [metrics, setMetrics] = useState<Metrics | null>(null);
  const [metricsPeriod, setMetricsPeriod] = useState('1h');
  
  // Health State
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);

  // Load system overview
  const loadSystemOverview = async () => {
    if (!hasPermission('monitoring.read')) return;
    
    setLoading(true);
    try {
      const response = await apiClient.getSystemOverview();
      if (response.success) {
        setSystemOverview(response.data);
      }
    } catch (error) {
      console.error('Failed to load system overview:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load metrics
  const loadMetrics = async () => {
    if (!hasPermission('monitoring.read')) return;
    
    try {
      const response = await apiClient.getMetrics(metricsPeriod);
      if (response.success) {
        setMetrics(response.data);
      }
    } catch (error) {
      console.error('Failed to load metrics:', error);
    }
  };

  // Load system health
  const loadSystemHealth = async () => {
    if (!hasPermission('monitoring.read')) return;
    
    try {
      const response = await apiClient.getSystemHealth();
      if (response.success) {
        setSystemHealth(response.data);
      }
    } catch (error) {
      console.error('Failed to load system health:', error);
    }
  };

  // Format duration
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Get health status color
  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'unhealthy': return 'text-red-600';
      case 'degraded': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  // Get health icon
  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'unhealthy': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'degraded': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  useEffect(() => {
    if (activeTab === 'overview') {
      loadSystemOverview();
      // Auto-refresh overview every 30 seconds
      const interval = setInterval(loadSystemOverview, 30000);
      return () => clearInterval(interval);
    } else if (activeTab === 'metrics') {
      loadMetrics();
    } else if (activeTab === 'health') {
      loadSystemHealth();
      // Auto-refresh health every 10 seconds
      const interval = setInterval(loadSystemHealth, 10000);
      return () => clearInterval(interval);
    }
  }, [activeTab, metricsPeriod]);

  return (
    <ProtectedRoute requiredPermission="monitoring.read">
      <div className="container mx-auto p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold">System Monitoring</h1>
            <p className="text-muted-foreground">Monitor system performance and health</p>
          </div>
          <Button onClick={() => {
            if (activeTab === 'overview') loadSystemOverview();
            else if (activeTab === 'metrics') loadMetrics();
            else if (activeTab === 'health') loadSystemHealth();
          }}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">System Overview</TabsTrigger>
            <TabsTrigger value="metrics">Metrics & Analytics</TabsTrigger>
            <TabsTrigger value="health">System Health</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {loading ? (
              <div className="text-center py-8">Loading system overview...</div>
            ) : systemOverview ? (
              <>
                {/* Users Stats */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                      <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{systemOverview.users.total}</div>
                      <p className="text-xs text-muted-foreground">
                        {systemOverview.users.online} online
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Active Calls</CardTitle>
                      <Phone className="h-4 w-4 text-blue-600" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">{systemOverview.calls.active}</div>
                      <p className="text-xs text-muted-foreground">
                        Currently ongoing
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Calls (24h)</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{systemOverview.calls.total24h}</div>
                      <p className="text-xs text-muted-foreground">
                        {systemOverview.calls.successRate}% success rate
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Avg Duration</CardTitle>
                      <Clock className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatDuration(systemOverview.calls.avgDuration)}</div>
                      <p className="text-xs text-muted-foreground">
                        Average call length
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Call Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Successful Calls</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-green-600">
                        {systemOverview.calls.successful}
                      </div>
                      <p className="text-sm text-muted-foreground">Last 24 hours</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Failed Calls</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold text-red-600">
                        {systemOverview.calls.failed}
                      </div>
                      <p className="text-sm text-muted-foreground">Last 24 hours</p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Success Rate</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-3xl font-bold">
                        {systemOverview.calls.successRate}%
                      </div>
                      <p className="text-sm text-muted-foreground">Last 24 hours</p>
                    </CardContent>
                  </Card>
                </div>

                {/* System Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>System Information</CardTitle>
                    <CardDescription>OpenSIPS server details</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <p className="text-sm font-medium">Uptime</p>
                        <p className="text-lg">{systemOverview.system.uptime}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Version</p>
                        <p className="text-lg">{systemOverview.system.version}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium">Memory</p>
                        <p className="text-lg">{systemOverview.system.memory}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                No system overview data available
              </div>
            )}
          </TabsContent>

          <TabsContent value="metrics" className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Metrics & Analytics</h3>
              <Select value={metricsPeriod} onValueChange={setMetricsPeriod}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">Last Hour</SelectItem>
                  <SelectItem value="6h">Last 6h</SelectItem>
                  <SelectItem value="24h">Last 24h</SelectItem>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {metrics ? (
              <div className="space-y-6">
                {/* Top Callers */}
                <Card>
                  <CardHeader>
                    <CardTitle>Top Callers</CardTitle>
                    <CardDescription>Most active callers in the selected period</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Caller</TableHead>
                            <TableHead>Total Calls</TableHead>
                            <TableHead>Successful</TableHead>
                            <TableHead>Success Rate</TableHead>
                            <TableHead>Avg Duration</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {metrics.topCallers.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={5} className="text-center">No data available</TableCell>
                            </TableRow>
                          ) : (
                            metrics.topCallers.map((caller, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">{caller.caller}</TableCell>
                                <TableCell>{caller.callCount}</TableCell>
                                <TableCell>{caller.successfulCalls}</TableCell>
                                <TableCell>
                                  <Badge variant={parseFloat(caller.successRate) > 80 ? 'default' : 'destructive'}>
                                    {caller.successRate}%
                                  </Badge>
                                </TableCell>
                                <TableCell>{formatDuration(caller.avgDuration)}</TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>

                {/* Error Distribution */}
                <Card>
                  <CardHeader>
                    <CardTitle>Error Distribution</CardTitle>
                    <CardDescription>Most common call failures</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>SIP Code</TableHead>
                            <TableHead>Reason</TableHead>
                            <TableHead>Count</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {metrics.errorDistribution.length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={3} className="text-center">No errors in this period</TableCell>
                            </TableRow>
                          ) : (
                            metrics.errorDistribution.map((error, index) => (
                              <TableRow key={index}>
                                <TableCell>
                                  <Badge variant="destructive">{error.code}</Badge>
                                </TableCell>
                                <TableCell>{error.reason}</TableCell>
                                <TableCell>{error.count}</TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                Loading metrics...
              </div>
            )}
          </TabsContent>

          <TabsContent value="health" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>System Health Status</CardTitle>
                <CardDescription>Real-time health monitoring of system components</CardDescription>
              </CardHeader>
              <CardContent>
                {systemHealth ? (
                  <div className="space-y-4">
                    {/* Overall Health */}
                    <div className="flex items-center space-x-2">
                      {getHealthIcon(systemHealth.overall)}
                      <span className="text-lg font-medium">Overall Status:</span>
                      <Badge
                        variant={systemHealth.overall === 'healthy' ? 'default' : 'destructive'}
                        className={getHealthColor(systemHealth.overall)}
                      >
                        {systemHealth.overall.toUpperCase()}
                      </Badge>
                    </div>

                    {/* Component Health */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Database</CardTitle>
                          <Database className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center space-x-2">
                            {getHealthIcon(systemHealth.database)}
                            <span className={`font-medium ${getHealthColor(systemHealth.database)}`}>
                              {systemHealth.database.toUpperCase()}
                            </span>
                          </div>
                          {systemHealth.databaseError && (
                            <p className="text-xs text-red-600 mt-1">{systemHealth.databaseError}</p>
                          )}
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">OpenSIPS</CardTitle>
                          <Server className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="flex items-center space-x-2">
                            {getHealthIcon(systemHealth.opensips)}
                            <span className={`font-medium ${getHealthColor(systemHealth.opensips)}`}>
                              {systemHealth.opensips.toUpperCase()}
                            </span>
                          </div>
                          {systemHealth.opensipsError && (
                            <p className="text-xs text-red-600 mt-1">{systemHealth.opensipsError}</p>
                          )}
                        </CardContent>
                      </Card>
                    </div>

                    <p className="text-xs text-muted-foreground">
                      Last updated: {new Date(systemHealth.timestamp).toLocaleString()}
                    </p>
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    Loading health status...
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </ProtectedRoute>
  );
}
