'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Phone, Users, Activity, Database, Server, PhoneCall, Wifi, WifiOff, TrendingUp } from 'lucide-react';
import { useSocket } from '@/hooks/useSocket';
import { useRealtimeStats } from '@/hooks/useRealtimeStats';
import { useRealtimeCalls } from '@/hooks/useRealtimeCalls';
import { useRealtimeUsers } from '@/hooks/useRealtimeUsers';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Bar, BarChart, Line, LineChart, Pie, Pie<PERSON>hart, Cell, ResponsiveContainer, XAxis, YAxis } from 'recharts';
import { ActivityFeed } from '@/components/activity-feed';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

interface Stats {
  activeRegistrations: number;
  activeDialogs: number;
  callsToday: number;
  callsLastHour: number;
  timestamp: string;
}

interface SystemStatus {
  database: {
    totalUsers: number;
    activeRegistrations: number;
    totalCalls: number;
    activeDialogs: number;
  };
  opensips: {
    available: boolean;
    uptime: any;
    statistics: any;
  };
  timestamp: string;
}

function DashboardContent() {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);

  // Use real-time hooks
  const { isConnected, isConnecting, connectionError } = useSocket();
  const { stats, lastUpdate: statsLastUpdate } = useRealtimeStats();
  const { calls } = useRealtimeCalls();
  const { users } = useRealtimeUsers();

  // Debug logging
  useEffect(() => {
    console.log('🔌 Dashboard Socket Status:', {
      isConnected,
      isConnecting,
      connectionError,
      stats,
      statsLastUpdate
    });
  }, [isConnected, isConnecting, connectionError, stats, statsLastUpdate]);

  const fetchInitialData = async () => {
    try {
      // Mock initial status data
      const mockStatus: SystemStatus = {
        database: {
          totalUsers: 5,
          activeRegistrations: 3,
          totalCalls: 150,
          activeDialogs: 1
        },
        opensips: {
          available: true,
          uptime: { uptime: '2 days, 5 hours' },
          statistics: null
        },
        timestamp: new Date().toISOString()
      };

      setStatus(mockStatus);
      setLoading(false);
    } catch (error) {
      console.error('Failed to fetch initial data:', error);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInitialData();
  }, []);

  // Update status when real-time data is available
  useEffect(() => {
    if (stats && status) {
      setStatus(prev => prev ? {
        ...prev,
        database: {
          ...prev.database,
          activeRegistrations: stats.activeRegistrations,
          activeDialogs: stats.activeDialogs,
          totalUsers: stats.totalUsers || prev.database.totalUsers
        },
        opensips: {
          ...prev.opensips,
          available: isConnected
        },
        timestamp: new Date().toISOString()
      } : prev);
    }
  }, [stats, isConnected]); // Removed 'status' from dependencies to prevent infinite loop

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Activity className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">OpenSIPS Dashboard</h1>
          <p className="text-muted-foreground">
            Real-time monitoring and management
          </p>
          {statsLastUpdate && (
            <p className="text-xs text-muted-foreground">
              Last update: {new Date(statsLastUpdate).toLocaleTimeString()}
            </p>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {/* Connection Status */}
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? (
              <>
                <Wifi className="h-3 w-3 mr-1" />
                Connected
              </>
            ) : isConnecting ? (
              <>
                <Activity className="h-3 w-3 mr-1 animate-spin" />
                Connecting...
              </>
            ) : (
              <>
                <WifiOff className="h-3 w-3 mr-1" />
                Disconnected
              </>
            )}
          </Badge>

          {/* Debug Info */}
          {connectionError && (
            <Badge variant="destructive" className="text-xs">
              Error: {connectionError}
            </Badge>
          )}

          {stats && (
            <Badge variant="secondary" className="text-xs">
              Stats: {stats.activeRegistrations} users
            </Badge>
          )}

          {/* OpenSIPS Status */}
          <Badge variant={status?.opensips.available ? "default" : "destructive"}>
            {status?.opensips.available ? "OpenSIPS Online" : "OpenSIPS Offline"}
          </Badge>

          <Button onClick={fetchInitialData} size="sm" disabled={isConnecting}>
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.activeRegistrations ?? status?.database.activeRegistrations ?? 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently registered
            </p>
            {users.length > 0 && (
              <p className="text-xs text-green-600">
                {users.length} users online
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Calls</CardTitle>
            <Phone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.activeDialogs ?? calls.length ?? status?.database.activeDialogs ?? 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Ongoing conversations
            </p>
            {calls.length > 0 && (
              <p className="text-xs text-blue-600">
                {calls.length} active dialogs
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Calls Today</CardTitle>
            <PhoneCall className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.callsToday ?? status?.database.totalCalls ?? 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Total calls made today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Hour</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats?.callsLastHour ?? 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Calls in last hour
            </p>
          </CardContent>
        </Card>
      </div>

      {/* System Status */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Server className="h-5 w-5 mr-2" />
              OpenSIPS Status
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Status:</span>
              <Badge variant={status?.opensips.available ? "default" : "destructive"}>
                {status?.opensips.available ? "Running" : "Stopped"}
              </Badge>
            </div>
            {status?.opensips.uptime && (
              <div className="flex justify-between">
                <span>Uptime:</span>
                <span className="text-sm">{status.opensips.uptime.uptime}</span>
              </div>
            )}
            <div className="flex justify-between">
              <span>Last Check:</span>
              <span className="text-sm">
                {status?.timestamp ? new Date(status.timestamp).toLocaleTimeString() : 'N/A'}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              Database Stats
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span>Total Users:</span>
              <span className="font-medium">{status?.database.totalUsers || 0}</span>
            </div>
            <div className="flex justify-between">
              <span>Active Registrations:</span>
              <span className="font-medium">{status?.database.activeRegistrations || 0}</span>
            </div>
            <div className="flex justify-between">
              <span>Total Calls:</span>
              <span className="font-medium">{status?.database.totalCalls || 0}</span>
            </div>
            <div className="flex justify-between">
              <span>Active Dialogs:</span>
              <span className="font-medium">{status?.database.activeDialogs || 0}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Hourly Call Distribution</CardTitle>
            <CardDescription>
              Calls per hour for the last 24 hours
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                calls: {
                  label: "Calls",
                  color: "hsl(var(--chart-1))",
                },
              }}
              className="h-[200px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={[
                    { hour: '00:00', calls: 5 },
                    { hour: '06:00', calls: 12 },
                    { hour: '12:00', calls: 25 },
                    { hour: '18:00', calls: 18 },
                    { hour: '23:00', calls: 8 }
                  ]}
                >
                  <XAxis dataKey="hour" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Bar dataKey="calls" fill="var(--color-calls)" />
                </BarChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Call Status Distribution</CardTitle>
            <CardDescription>
              Success vs Failed calls today
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                successful: {
                  label: "Successful",
                  color: "hsl(var(--chart-2))",
                },
                failed: {
                  label: "Failed",
                  color: "hsl(var(--chart-3))",
                },
              }}
              className="h-[200px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={[
                      {
                        name: 'Successful',
                        value: stats?.callsToday ? Math.round(stats.callsToday * 0.8) : 12,
                        fill: "var(--color-successful)"
                      },
                      {
                        name: 'Failed',
                        value: stats?.callsToday ? Math.round(stats.callsToday * 0.2) : 3,
                        fill: "var(--color-failed)"
                      }
                    ]}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    dataKey="value"
                  />
                  <ChartTooltip content={<ChartTooltipContent />} />
                </PieChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Real-time Activity
            </CardTitle>
            <CardDescription>
              System activity over time
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ChartContainer
              config={{
                activity: {
                  label: "Active Users",
                  color: "hsl(var(--chart-4))",
                },
              }}
              className="h-[200px]"
            >
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={[
                    { time: '10:00', activity: stats?.activeRegistrations || 3 },
                    { time: '10:30', activity: (stats?.activeRegistrations || 3) + 1 },
                    { time: '11:00', activity: (stats?.activeRegistrations || 3) + 2 },
                    { time: '11:30', activity: stats?.activeRegistrations || 3 },
                    { time: '12:00', activity: (stats?.activeRegistrations || 3) - 1 }
                  ]}
                >
                  <XAxis dataKey="time" />
                  <YAxis />
                  <ChartTooltip content={<ChartTooltipContent />} />
                  <Line
                    type="monotone"
                    dataKey="activity"
                    stroke="var(--color-activity)"
                    strokeWidth={2}
                    dot={{ fill: "var(--color-activity)" }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </ChartContainer>
          </CardContent>
        </Card>

        {/* Activity Feed */}
        <ActivityFeed />
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common management tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-16 flex flex-col">
              <Users className="h-6 w-6 mb-2" />
              View Users
            </Button>
            <Button variant="outline" className="h-16 flex flex-col">
              <Phone className="h-6 w-6 mb-2" />
              Call Logs
            </Button>
            <Button variant="outline" className="h-16 flex flex-col">
              <Server className="h-6 w-6 mb-2" />
              System Config
            </Button>
            <Button variant="outline" className="h-16 flex flex-col">
              <Activity className="h-6 w-6 mb-2" />
              Monitoring
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function Dashboard() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
