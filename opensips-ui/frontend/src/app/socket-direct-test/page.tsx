'use client';

import { useEffect, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export default function SocketDirectTestPage() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);
  const [stats, setStats] = useState<any>(null);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 49)]);
    console.log(`[${timestamp}] ${message}`);
  };

  const connectSocket = () => {
    if (socket?.connected) {
      addLog('Already connected');
      return;
    }

    addLog('🔄 Connecting to Socket.IO server...');
    
    const newSocket = io('http://localhost:3001', {
      transports: ['polling', 'websocket'],
      timeout: 10000,
      forceNew: true,
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000
    });

    newSocket.on('connect', () => {
      addLog('✅ Socket connected successfully with ID: ' + newSocket.id);
      setIsConnected(true);
    });

    newSocket.on('connect_error', (error) => {
      addLog('❌ Socket connection error: ' + error.message);
      setIsConnected(false);
    });

    newSocket.on('disconnect', (reason) => {
      addLog('🔌 Socket disconnected: ' + reason);
      setIsConnected(false);
    });

    newSocket.on('welcome', (data) => {
      addLog('👋 Welcome message: ' + JSON.stringify(data));
    });

    newSocket.on('stats-update', (data) => {
      addLog('📊 Stats update: ' + JSON.stringify(data));
      setStats(data);
    });

    setSocket(newSocket);
  };

  const subscribeToStats = () => {
    if (!socket?.connected) {
      addLog('❌ Socket not connected');
      return;
    }

    addLog('📊 Subscribing to stats...');
    socket.emit('subscribe-stats');
  };

  const disconnectSocket = () => {
    if (socket) {
      addLog('🔌 Disconnecting socket...');
      socket.disconnect();
      setSocket(null);
      setIsConnected(false);
      setStats(null);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  useEffect(() => {
    addLog('🚀 Component mounted');
    
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Direct Socket.IO Test</h1>
        <div className="flex gap-2">
          <Button onClick={clearLogs} variant="outline">Clear Logs</Button>
          <Button onClick={connectSocket} disabled={isConnected}>
            Connect
          </Button>
          <Button onClick={subscribeToStats} disabled={!isConnected}>
            Subscribe Stats
          </Button>
          <Button onClick={disconnectSocket} disabled={!isConnected} variant="destructive">
            Disconnect
          </Button>
        </div>
      </div>

      {/* Status */}
      <div className="flex gap-4">
        <Badge variant={isConnected ? "default" : "destructive"}>
          {isConnected ? "Connected" : "Disconnected"}
        </Badge>
        {socket?.id && (
          <Badge variant="secondary">
            ID: {socket.id}
          </Badge>
        )}
      </div>

      {/* Stats */}
      {stats && (
        <Card>
          <CardHeader>
            <CardTitle>Latest Stats</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(stats, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}

      {/* Logs */}
      <Card>
        <CardHeader>
          <CardTitle>Connection Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-4 rounded-md h-96 overflow-y-auto font-mono text-sm">
            {logs.length === 0 ? (
              <p className="text-gray-500">No logs yet...</p>
            ) : (
              logs.map((log, index) => (
                <div key={index} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent>
          <ol className="list-decimal list-inside space-y-2 text-sm">
            <li>Click "Connect" to establish Socket.IO connection</li>
            <li>Click "Subscribe Stats" to subscribe to real-time stats</li>
            <li>Watch logs for connection status and data updates</li>
            <li>Compare with socket-test.html behavior</li>
          </ol>
        </CardContent>
      </Card>
    </div>
  );
}
