'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Phone, 
  PhoneCall, 
  UserPlus, 
  UserMinus, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Activity
} from 'lucide-react';
import { useRealtimeEvents } from '@/hooks/useSocket';

interface ActivityEvent {
  id: string;
  type: 'call' | 'registration' | 'unregistration' | 'error' | 'system';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'error' | 'warning' | 'info';
  details?: {
    from?: string;
    to?: string;
    code?: string;
    reason?: string;
  };
}

export function ActivityFeed() {
  const [activities, setActivities] = useState<ActivityEvent[]>([]);
  const { events, lastUpdate } = useRealtimeEvents();

  // Convert real-time events to activity format
  useEffect(() => {
    if (events && events.length > 0) {
      const newActivities = events.map((event, index) => ({
        id: `${event.timestamp}-${index}`,
        type: event.type as ActivityEvent['type'],
        title: getEventTitle(event),
        description: getEventDescription(event),
        timestamp: event.event_time,
        status: getEventStatus(event.sip_code),
        details: {
          from: event.source,
          to: event.destination,
          code: event.sip_code?.toString(),
          reason: event.sip_reason
        }
      }));
      setActivities(newActivities);
    } else {
      // Mock activities when no real-time data
      setActivities([
        {
          id: '1',
          type: 'call',
          title: 'Call Established',
          description: '1001 → 1002',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          status: 'success',
          details: { from: '1001', to: '1002', code: '200', reason: 'OK' }
        },
        {
          id: '2',
          type: 'registration',
          title: 'User Registered',
          description: '1003 registered successfully',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          status: 'success',
          details: { from: '1003', code: '200', reason: 'OK' }
        },
        {
          id: '3',
          type: 'call',
          title: 'Call Failed',
          description: '1004 → 1001',
          timestamp: new Date(Date.now() - 900000).toISOString(),
          status: 'error',
          details: { from: '1004', to: '1001', code: '486', reason: 'Busy Here' }
        },
        {
          id: '4',
          type: 'system',
          title: 'System Health Check',
          description: 'All services running normally',
          timestamp: new Date(Date.now() - 1200000).toISOString(),
          status: 'info'
        }
      ]);
    }
  }, [events]);

  const getEventTitle = (event: any): string => {
    switch (event.type) {
      case 'call':
        return event.sip_code === 200 ? 'Call Established' : 'Call Failed';
      case 'registration':
        return 'User Registered';
      default:
        return 'System Event';
    }
  };

  const getEventDescription = (event: any): string => {
    switch (event.type) {
      case 'call':
        return `${event.source} → ${event.destination}`;
      case 'registration':
        return `${event.source} registered successfully`;
      default:
        return event.method || 'System activity';
    }
  };

  const getEventStatus = (sipCode: number): ActivityEvent['status'] => {
    if (sipCode >= 200 && sipCode < 300) return 'success';
    if (sipCode >= 400 && sipCode < 500) return 'warning';
    if (sipCode >= 500) return 'error';
    return 'info';
  };

  const getEventIcon = (type: ActivityEvent['type'], status: ActivityEvent['status']) => {
    switch (type) {
      case 'call':
        return status === 'success' ? 
          <PhoneCall className="h-4 w-4 text-green-600" /> : 
          <Phone className="h-4 w-4 text-red-600" />;
      case 'registration':
        return <UserPlus className="h-4 w-4 text-blue-600" />;
      case 'unregistration':
        return <UserMinus className="h-4 w-4 text-orange-600" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'system':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusBadge = (status: ActivityEvent['status']) => {
    const variants = {
      success: 'default',
      error: 'destructive',
      warning: 'secondary',
      info: 'outline'
    } as const;

    return (
      <Badge variant={variants[status]} className="text-xs">
        {status}
      </Badge>
    );
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMins / 60);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return date.toLocaleDateString();
  };

  return (
    <Card className="h-[400px]">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              Activity Feed
            </CardTitle>
            <CardDescription>
              Recent system events and activities
            </CardDescription>
          </div>
          {lastUpdate && (
            <div className="text-xs text-muted-foreground">
              Updated: {new Date(lastUpdate).toLocaleTimeString()}
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[280px]">
          <div className="space-y-3">
            {activities.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recent activity</p>
              </div>
            ) : (
              activities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex-shrink-0 mt-1">
                    {getEventIcon(activity.type, activity.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">{activity.title}</h4>
                      <div className="flex items-center space-x-2">
                        {getStatusBadge(activity.status)}
                        <span className="text-xs text-muted-foreground">
                          {formatTime(activity.timestamp)}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {activity.description}
                    </p>
                    {activity.details && (activity.details.code || activity.details.reason) && (
                      <div className="flex items-center space-x-2 mt-1 text-xs text-muted-foreground">
                        {activity.details.code && (
                          <span className="bg-muted px-2 py-1 rounded">
                            {activity.details.code}
                          </span>
                        )}
                        {activity.details.reason && (
                          <span>{activity.details.reason}</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
