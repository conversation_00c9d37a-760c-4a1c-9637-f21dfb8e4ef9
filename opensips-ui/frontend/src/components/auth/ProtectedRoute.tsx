'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Loader2, Lock, AlertTriangle, RefreshCw } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: 'admin' | 'operator' | 'viewer';
  requiredPermission?: string;
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermission,
  fallback,
}) => {
  const { isAuthenticated, isLoading, user, hasRole, hasPermission } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login');
    }
  }, [isAuthenticated, isLoading, router]);

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
              <div>
                <h3 className="text-lg font-semibold">Loading...</h3>
                <p className="text-sm text-muted-foreground">
                  Verifying your session
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <Lock className="h-8 w-8 mx-auto text-red-600" />
              <div>
                <h3 className="text-lg font-semibold">Access Denied</h3>
                <p className="text-sm text-muted-foreground">
                  You need to sign in to access this page
                </p>
              </div>
              <Button onClick={() => router.push('/login')}>
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check role requirement
  if (requiredRole && !hasRole(requiredRole)) {
    const unauthorizedContent = (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <AlertTriangle className="h-8 w-8 mx-auto text-amber-600" />
              <div>
                <h3 className="text-lg font-semibold">Insufficient Permissions</h3>
                <p className="text-sm text-muted-foreground">
                  You need <strong>{requiredRole}</strong> role to access this page.
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Your current role: <strong>{user?.role}</strong>
                </p>
              </div>
              <div className="space-y-2">
                <Button variant="outline" onClick={() => router.back()}>
                  Go Back
                </Button>
                <Button variant="outline" onClick={() => router.push('/')}>
                  Go to Dashboard
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );

    return fallback || unauthorizedContent;
  }

  // Check permission requirement
  if (requiredPermission && !hasPermission(requiredPermission)) {
    const unauthorizedContent = (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <AlertTriangle className="h-8 w-8 mx-auto text-amber-600" />
              <div>
                <h3 className="text-lg font-semibold">Access Restricted</h3>
                <p className="text-sm text-muted-foreground">
                  You don't have permission to access this feature.
                </p>
                <p className="text-xs text-muted-foreground mt-2">
                  Required permission: <strong>{requiredPermission}</strong>
                </p>
              </div>
              <div className="space-y-2">
                <Button variant="outline" onClick={() => router.back()}>
                  Go Back
                </Button>
                <Button variant="outline" onClick={() => router.push('/')}>
                  Go to Dashboard
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );

    return fallback || unauthorizedContent;
  }

  // All checks passed, render children
  return <>{children}</>;
};

// Higher-order component for easier usage
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredRole?: 'admin' | 'operator' | 'viewer';
    requiredPermission?: string;
  }
) => {
  const AuthenticatedComponent = (props: P) => (
    <ProtectedRoute
      requiredRole={options?.requiredRole}
      requiredPermission={options?.requiredPermission}
    >
      <Component {...props} />
    </ProtectedRoute>
  );

  AuthenticatedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return AuthenticatedComponent;
};

// Permission check component for conditional rendering
interface PermissionGateProps {
  children: ReactNode;
  role?: 'admin' | 'operator' | 'viewer';
  permission?: string;
  fallback?: ReactNode;
}

export const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  role,
  permission,
  fallback = null,
}) => {
  const { hasRole, hasPermission } = useAuth();

  if (role && !hasRole(role)) {
    return <>{fallback}</>;
  }

  if (permission && !hasPermission(permission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};
