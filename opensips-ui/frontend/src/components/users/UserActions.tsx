'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Edit, Trash2, UserX, Key, Copy, Phone } from 'lucide-react';
import { User } from '@/types/user';
import { useAuth } from '@/contexts/AuthContext';
import apiClient from '@/lib/api';

interface UserActionsProps {
  user: User;
  onEdit: (user: User) => void;
  onRefresh: () => void;
}

export function UserActions({ user, onEdit, onRefresh }: UserActionsProps) {
  const { hasPermission } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleDelete = async () => {
    if (!confirm(`Are you sure you want to delete user ${user.username}@${user.domain}?`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiClient.deleteSipUser(user.username, user.domain);
      if (response.success) {
        alert('User deleted successfully');
        onRefresh();
      } else {
        alert(response.message || 'Failed to delete user');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to delete user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKick = async () => {
    if (!confirm(`Are you sure you want to kick user ${user.username}@${user.domain}?`)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiClient.kickSipUser(user.username, user.domain);
      if (response.success) {
        alert('User kicked successfully');
        onRefresh();
      } else {
        alert(response.message || 'Failed to kick user');
      }
    } catch (error: any) {
      alert(error.message || 'Failed to kick user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopyCredentials = () => {
    const credentials = `Username: ${user.username}@${user.domain}`;
    navigator.clipboard.writeText(credentials);
    alert('Credentials copied to clipboard');
  };

  const handleCall = () => {
    // This would integrate with WebRTC or call initiation
    alert('Call feature coming soon');
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          
          <DropdownMenuItem onClick={handleCopyCredentials}>
            <Copy className="mr-2 h-4 w-4" />
            Copy Credentials
          </DropdownMenuItem>

          {hasPermission('calls.write') && (
            <DropdownMenuItem onClick={handleCall}>
              <Phone className="mr-2 h-4 w-4" />
              Initiate Call
            </DropdownMenuItem>
          )}

          <DropdownMenuSeparator />

          {hasPermission('users.write') && (
            <>
              <DropdownMenuItem onClick={() => onEdit(user)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit User
              </DropdownMenuItem>

              {user.isRegistered && (
                <DropdownMenuItem onClick={handleKick} disabled={isLoading}>
                  <UserX className="mr-2 h-4 w-4" />
                  Kick User
                </DropdownMenuItem>
              )}
            </>
          )}

          {hasPermission('users.delete') && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleDelete}
                disabled={isLoading}
                className="text-red-600 focus:text-red-600"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete User
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
}
