'use client';

import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Phone, 
  PhoneOff, 
  Clock, 
  Globe, 
  Smartphone, 
  MapPin,
  MoreVertical,
  MessageCircle,
  UserCheck
} from 'lucide-react';
import { User } from '@/types/user';
import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';

interface UserCardProps {
  user: User;
  onCall?: (user: User) => void;
  onMessage?: (user: User) => void;
  onViewDetails?: (user: User) => void;
  actions?: React.ReactNode;
}

export function UserCard({ user, onCall, onMessage, onViewDetails, actions }: UserCardProps) {
  const isOnline = user.status === 'online';
  
  const getStatusColor = () => {
    return isOnline ? 'bg-green-500' : 'bg-gray-400';
  };

  const getStatusBadgeVariant = () => {
    return isOnline ? 'default' : 'secondary';
  };

  const formatLastSeen = () => {
    if (!user.last_seen_formatted && !user.last_seen) return 'Chưa rõ';

    try {
      // Try formatted date first, then fallback to timestamp
      const dateStr = user.last_seen_formatted || user.last_seen;
      const date = new Date(dateStr);

      if (isNaN(date.getTime())) {
        // If it's a timestamp, convert it
        const timestamp = parseInt(dateStr as string) * 1000;
        const timestampDate = new Date(timestamp);
        if (!isNaN(timestampDate.getTime())) {
          return formatDistanceToNow(timestampDate, {
            addSuffix: true,
            locale: vi
          });
        }
        return 'Chưa rõ';
      }

      return formatDistanceToNow(date, {
        addSuffix: true,
        locale: vi
      });
    } catch {
      return 'Chưa rõ';
    }
  };

  const formatExpiresIn = () => {
    if (!user.expires_in_seconds || user.expires_in_seconds <= 0) return null;
    
    const minutes = Math.floor(user.expires_in_seconds / 60);
    const seconds = user.expires_in_seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}p ${seconds}s`;
    }
    return `${seconds}s`;
  };

  const getUserInitials = () => {
    return user.username.slice(0, 2).toUpperCase();
  };

  const getUserAgent = () => {
    if (!user.user_agent) return 'Không rõ';
    
    // Shorten common user agents
    const ua = user.user_agent;
    if (ua.includes('Zoiper')) return 'Zoiper';
    if (ua.includes('MicroSIP')) return 'MicroSIP';
    if (ua.includes('Linphone')) return 'Linphone';
    if (ua.includes('X-Lite')) return 'X-Lite';
    if (ua.includes('Bria')) return 'Bria';
    
    return ua.length > 20 ? ua.substring(0, 20) + '...' : ua;
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-lg ${isOnline ? 'border-green-200 bg-green-50/30' : 'border-gray-200'}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar className="h-10 w-10">
                <AvatarFallback className={`${isOnline ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}`}>
                  {getUserInitials()}
                </AvatarFallback>
              </Avatar>
              <div className={`absolute -bottom-1 -right-1 h-4 w-4 rounded-full border-2 border-white ${getStatusColor()}`} />
            </div>
            
            <div>
              <CardTitle className="text-lg font-semibold">
                {user.username}
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                @{user.domain}
              </p>
            </div>
          </div>
          
          <Badge variant={getStatusBadgeVariant()} className="capitalize">
            {isOnline ? (
              <><Phone className="w-3 h-3 mr-1" /> Online</>
            ) : (
              <><PhoneOff className="w-3 h-3 mr-1" /> Offline</>
            )}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Contact Info */}
        <div className="space-y-2">
          {user.ip_address && (
            <div className="flex items-center text-sm text-muted-foreground">
              <MapPin className="w-4 h-4 mr-2" />
              <span>{user.ip_address}</span>
            </div>
          )}
          
          <div className="flex items-center text-sm text-muted-foreground">
            <Smartphone className="w-4 h-4 mr-2" />
            <span>{getUserAgent()}</span>
          </div>
          
          <div className="flex items-center text-sm text-muted-foreground">
            <Clock className="w-4 h-4 mr-2" />
            <span>Lần cuối: {formatLastSeen()}</span>
          </div>
          
          {isOnline && formatExpiresIn() && (
            <div className="flex items-center text-sm text-green-600">
              <UserCheck className="w-4 h-4 mr-2" />
              <span>Hết hạn sau: {formatExpiresIn()}</span>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex space-x-2 pt-2">
          {isOnline && onCall && (
            <Button 
              size="sm" 
              variant="default"
              onClick={() => onCall(user)}
              className="flex-1"
            >
              <Phone className="w-4 h-4 mr-1" />
              Gọi
            </Button>
          )}
          
          {onMessage && (
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => onMessage(user)}
              className="flex-1"
            >
              <MessageCircle className="w-4 h-4 mr-1" />
              Tin nhắn
            </Button>
          )}
          
          {actions ? (
            actions
          ) : onViewDetails && (
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onViewDetails(user)}
            >
              <MoreVertical className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
