'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  UserCheck, 
  UserX, 
  Smartphone, 
  Globe,
  Clock,
  TrendingUp
} from 'lucide-react';
import { UsersStatsResponse } from '@/types/user';

interface UserStatsProps {
  stats: UsersStatsResponse | null;
  loading?: boolean;
}

export function UserStats({ stats, loading }: UserStatsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">
            Không thể tải thống kê người dùng
          </p>
        </CardContent>
      </Card>
    );
  }

  const { overview, userAgents, domains } = stats;
  const onlinePercentage = overview.total_users > 0 
    ? Math.round((overview.online_users / overview.total_users) * 100) 
    : 0;

  const formatTime = (seconds?: number) => {
    if (!seconds) return 'N/A';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
      return `${minutes}p ${remainingSeconds}s`;
    }
    return `${remainingSeconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Tổng người dùng
            </CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overview.total_users}</div>
            <p className="text-xs text-muted-foreground">
              Đã đăng ký trong hệ thống
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Đang online
            </CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {overview.online_users}
            </div>
            <div className="flex items-center space-x-2">
              <Progress value={onlinePercentage} className="flex-1" />
              <span className="text-xs text-muted-foreground">
                {onlinePercentage}%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Offline
            </CardTitle>
            <UserX className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">
              {overview.offline_users}
            </div>
            <p className="text-xs text-muted-foreground">
              Không hoạt động
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Thời gian đăng ký TB
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatTime(overview.avg_expires_in)}
            </div>
            <p className="text-xs text-muted-foreground">
              Thời gian còn lại trung bình
            </p>
          </CardContent>
        </Card>
      </div>

      {/* User Agents & Domains */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Agents */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Smartphone className="h-5 w-5 mr-2" />
              Ứng dụng SIP phổ biến
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {userAgents.slice(0, 5).map((ua, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium truncate max-w-[200px]">
                      {ua.user_agent || 'Không rõ'}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {ua.count}
                    </Badge>
                    {ua.online_count > 0 && (
                      <Badge variant="default" className="text-xs bg-green-500">
                        {ua.online_count} online
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
              
              {userAgents.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  Chưa có dữ liệu ứng dụng SIP
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Domains */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Globe className="h-5 w-5 mr-2" />
              Phân bố theo domain
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {domains.map((domain, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span className="text-sm font-medium">
                      {domain.domain}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className="text-xs">
                      {domain.total_users} total
                    </Badge>
                    {domain.online_users > 0 && (
                      <Badge variant="default" className="text-xs bg-green-500">
                        {domain.online_users} online
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
              
              {domains.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  Chưa có dữ liệu domain
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
