'use client';

import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { socketService } from '@/lib/socket';

interface Call {
  id: string;
  method: string;
  from_tag: string;
  to_tag: string;
  call_id: string;
  sip_code: string;
  sip_reason: string;
  time: number;
  call_time: string;
  duration: number;
  setuptime: number;
  created: number;
  created_at: string;
}

export const useRealtimeCalls = () => {
  const [calls, setCalls] = useState<Call[]>([]);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Use the shared socket connection
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) {
      console.log('📞 useRealtimeCalls: Socket not connected, waiting...');
      return;
    }

    console.log('📞 useRealtimeCalls: Socket connected, setting up calls listener...');

    const handleCallsUpdate = (data: any) => {
      console.log('📞 useRealtimeCalls: Received calls update:', data);
      // Handle both direct data and wrapped data
      const callsData = data.data || data;
      setCalls(Array.isArray(callsData) ? callsData : []);
      setLastUpdate(new Date().toISOString());
      setError(null);
    };

    // Subscribe to calls updates using socketService
    socketService.subscribeToCalls(handleCallsUpdate);

    console.log('📞 Subscribed to calls updates');

    // Cleanup
    return () => {
      console.log('📞 Cleaning up calls subscription...');
      socketService.unsubscribeFromCalls();
    };
  }, [isConnected]);

  return {
    calls,
    lastUpdate,
    error
  };
};
