'use client';

import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { socketService } from '@/lib/socket';

interface RealtimeStats {
  activeRegistrations: number;
  activeDialogs: number;
  callsToday: number;
  callsLastHour: number;
  totalUsers: number;
}

export const useRealtimeStats = () => {
  const [stats, setStats] = useState<RealtimeStats | null>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Use the shared socket connection
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) {
      console.log('📊 useRealtimeStats: Socket not connected, waiting...');
      return;
    }

    console.log('📊 useRealtimeStats: Socket connected, setting up stats listener...');

    const handleStatsUpdate = (data: any) => {
      console.log('📊 useRealtimeStats: Received stats update:', data);
      // Handle both direct data and wrapped data
      const statsData = data.data || data;
      setStats(statsData);
      setLastUpdate(new Date().toISOString());
      setError(null);
    };

    const handleStatsError = (error: any) => {
      console.error('❌ Stats error:', error);
      setError(error.message || 'Failed to get stats');
    };

    // Subscribe to stats updates using socketService
    socketService.subscribeToStats(handleStatsUpdate);

    console.log('📊 Subscribed to stats updates');

    // Cleanup
    return () => {
      console.log('📊 Cleaning up stats subscription...');
      socketService.unsubscribeFromStats();
    };
  }, [isConnected]);

  return {
    stats,
    lastUpdate,
    error
  };
};
