'use client';

import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { socketService } from '@/lib/socket';

interface User {
  id: string;
  username: string;
  domain: string;
  email_address: string;
  contact?: string;
  expires?: number;
  last_modified?: number;
  status: 'online' | 'offline';
  expires_at?: string;
  last_seen?: string;
}

export const useRealtimeUsers = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Use the shared socket connection
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) {
      console.log('👥 useRealtimeUsers: Socket not connected, waiting...');
      return;
    }

    console.log('👥 useRealtimeUsers: Socket connected, setting up users listener...');

    const handleUsersUpdate = (data: any) => {
      console.log('👥 useRealtimeUsers: Received users update:', data);
      // Handle both direct data and wrapped data
      const usersData = data.data || data;
      setUsers(Array.isArray(usersData) ? usersData : []);
      setLastUpdate(new Date().toISOString());
      setError(null);
    };

    // Subscribe to users updates using socketService
    socketService.subscribeToUsers(handleUsersUpdate);

    console.log('👥 Subscribed to users updates');

    // Cleanup
    return () => {
      console.log('👥 Cleaning up users subscription...');
      socketService.unsubscribeFromUsers();
    };
  }, [isConnected]);

  return {
    users,
    lastUpdate,
    error
  };
};
