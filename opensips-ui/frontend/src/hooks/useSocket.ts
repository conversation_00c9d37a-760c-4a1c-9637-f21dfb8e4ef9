import { useEffect, useState, useCallback, useRef } from 'react';
import socketService from '@/lib/socket';

interface UseSocketOptions {
  autoConnect?: boolean;
  reconnectOnMount?: boolean;
}

export const useSocket = (options: UseSocketOptions = {}) => {
  const { autoConnect = true, reconnectOnMount = true } = options;
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const mountedRef = useRef(true);

  const connect = useCallback(async () => {
    if (isConnecting) {
      console.log('⏳ Already connecting, skipping...');
      return;
    }

    if (isConnected) {
      console.log('✅ Already connected, skipping...');
      return;
    }

    console.log('🔄 Starting socket connection from useSocket hook...');
    console.log('🔧 Socket service URL:', socketService.getConnectionInfo().url);
    setIsConnecting(true);
    setConnectionError(null);

    try {
      console.log('📞 Calling socketService.connect()...');
      await socketService.connect();
      if (mountedRef.current) {
        setIsConnected(true);
        setConnectionError(null);
        console.log('✅ Socket connected successfully in useSocket');
      }
    } catch (error) {
      console.error('❌ Socket connection failed in useSocket:', error);
      if (mountedRef.current) {
        setConnectionError(error instanceof Error ? error.message : 'Connection failed');
        setIsConnected(false);
      }
    } finally {
      if (mountedRef.current) {
        setIsConnecting(false);
      }
    }
  }, [isConnecting, isConnected]);

  const disconnect = useCallback(() => {
    socketService.disconnect();
    setIsConnected(false);
    setConnectionError(null);
  }, []);

  useEffect(() => {
    mountedRef.current = true;

    // Auto connect on mount
    if (autoConnect) {
      connect();
    }

    return () => {
      mountedRef.current = false;
    };
  }, [autoConnect, connect]);

  // Separate effect for socket event listeners
  useEffect(() => {
    const socket = socketService.getSocket();
    if (socket) {
      const handleConnect = () => {
        console.log('🔌 useSocket: Socket connected with ID:', socket.id);
        if (mountedRef.current) {
          setIsConnected(true);
          setConnectionError(null);
        }
      };

      const handleDisconnect = () => {
        if (mountedRef.current) {
          setIsConnected(false);
        }
      };

      const handleError = (error: any) => {
        if (mountedRef.current) {
          setConnectionError(error.message || 'Connection error');
        }
      };

      socket.on('connect', handleConnect);
      socket.on('disconnect', handleDisconnect);
      socket.on('connect_error', handleError);

      return () => {
        socket.off('connect', handleConnect);
        socket.off('disconnect', handleDisconnect);
        socket.off('connect_error', handleError);
      };
    }
  }, []); // Empty dependency array since we only want to set up listeners once

  return {
    isConnected,
    isConnecting,
    connectionError,
    connect,
    disconnect,
    socket: socketService.getSocket()
  };
};



// Hook for real-time stats
export const useRealtimeStats = () => {
  const [stats, setStats] = useState<any>(null);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) return;

    const handleStatsUpdate = (data: any) => {
      setStats(data.data || data);
      setLastUpdate(new Date().toISOString());
    };

    socketService.subscribeToStats(handleStatsUpdate);

    return () => {
      socketService.unsubscribeFromStats();
    };
  }, [isConnected]);

  return { stats, lastUpdate };
};

// Hook for real-time events
export const useRealtimeEvents = () => {
  const [events, setEvents] = useState<any[]>([]);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) return;

    const handleEventsUpdate = (data: any) => {
      setEvents(data.data || data);
      setLastUpdate(new Date().toISOString());
    };

    socketService.subscribeToEvents(handleEventsUpdate);

    return () => {
      socketService.unsubscribeFromEvents();
    };
  }, [isConnected]);

  return { events, lastUpdate };
};

// Hook for real-time users
export const useRealtimeUsers = () => {
  const [users, setUsers] = useState<any[]>([]);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) return;

    const handleUsersUpdate = (data: any) => {
      setUsers(data.data?.users || data.users || data);
      setLastUpdate(new Date().toISOString());
    };

    socketService.subscribeToUsers(handleUsersUpdate);

    return () => {
      socketService.unsubscribeFromUsers();
    };
  }, [isConnected]);

  return { users, lastUpdate };
};

// Hook for real-time calls
export const useRealtimeCalls = () => {
  const [calls, setCalls] = useState<any[]>([]);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) return;

    const handleCallsUpdate = (data: any) => {
      setCalls(data.data?.calls || data.calls || data);
      setLastUpdate(new Date().toISOString());
    };

    socketService.subscribeToCalls(handleCallsUpdate);

    return () => {
      socketService.unsubscribeFromCalls();
    };
  }, [isConnected]);

  return { calls, lastUpdate };
};

// Generic hook for custom socket events
export const useSocketEvent = (event: string, callback: (data: any) => void) => {
  const { isConnected } = useSocket();

  useEffect(() => {
    if (!isConnected) return;

    socketService.on(event, callback);

    return () => {
      socketService.off(event, callback);
    };
  }, [event, isConnected]); // Removed callback from dependencies to prevent re-subscription
};
