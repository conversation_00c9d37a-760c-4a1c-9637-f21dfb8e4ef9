import config from './config';

const API_BASE_URL = config.apiUrl;

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}/api${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }));
      throw new Error(error.error || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // Auth endpoints
  async login(username: string, password: string) {
    return this.request<{
      success: boolean;
      token: string;
      user: any;
    }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password }),
    });
  }

  async register(userData: {
    username: string;
    password: string;
    email?: string;
    domain?: string;
  }) {
    return this.request<{
      success: boolean;
      message: string;
      user: any;
    }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async verifyToken() {
    return this.request<{
      success: boolean;
      user: any;
    }>('/auth/verify');
  }

  async getCurrentUser() {
    return this.request<{
      success: boolean;
      data: { user: any };
    }>('/auth/me');
  }

  async logout() {
    return this.request<{
      success: boolean;
      message: string;
    }>('/auth/logout', {
      method: 'POST',
    });
  }

  // Users endpoints
  async getUsers() {
    return this.request<{
      success: boolean;
      users: any[];
    }>('/users');
  }

  async getUser(id: string) {
    return this.request<{
      success: boolean;
      user: any;
    }>(`/users/${id}`);
  }

  async createUser(userData: {
    username: string;
    password: string;
    email?: string;
    domain?: string;
  }) {
    return this.request<{
      success: boolean;
      message: string;
      user: any;
    }>('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(id: string, userData: {
    password?: string;
    email?: string;
  }) {
    return this.request<{
      success: boolean;
      message: string;
    }>(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id: string) {
    return this.request<{
      success: boolean;
      message: string;
    }>(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  async getOnlineUsers() {
    return this.request<{
      success: boolean;
      users: any[];
    }>('/users/status/online');
  }

  // Calls endpoints
  async getCalls(params?: {
    page?: number;
    limit?: number;
    from?: string;
    to?: string;
    method?: string;
  }) {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }
    
    const query = searchParams.toString();
    return this.request<{
      success: boolean;
      calls: any[];
      pagination: any;
    }>(`/calls${query ? `?${query}` : ''}`);
  }

  async getCallStats(period: string = 'today') {
    return this.request<{
      success: boolean;
      stats: any;
      period: string;
    }>(`/calls/stats?period=${period}`);
  }

  async getActiveCalls() {
    return this.request<{
      success: boolean;
      activeCalls: any[];
    }>('/calls/active');
  }

  async getCall(id: string) {
    return this.request<{
      success: boolean;
      call: any;
    }>(`/calls/${id}`);
  }

  async getHourlyStats(date?: string) {
    const query = date ? `?date=${date}` : '';
    return this.request<{
      success: boolean;
      hourlyStats: any[];
      date: string;
    }>(`/calls/stats/hourly${query}`);
  }

  // Monitoring endpoints
  async getSystemStatus() {
    return this.request<{
      success: boolean;
      status: any;
    }>('/monitoring/status');
  }

  async getRealtimeStats() {
    return this.request<{
      success: boolean;
      stats: any;
    }>('/monitoring/stats/realtime');
  }

  async getProcesses() {
    return this.request<{
      success: boolean;
      processes: any;
    }>('/monitoring/processes');
  }

  async getMemoryUsage() {
    return this.request<{
      success: boolean;
      memory: any;
    }>('/monitoring/memory');
  }

  async reloadConfig() {
    return this.request<{
      success: boolean;
      message: string;
    }>('/monitoring/reload', {
      method: 'POST',
    });
  }

  async getDomains() {
    return this.request<{
      success: boolean;
      domains: any[];
    }>('/monitoring/domains');
  }

  async getEvents(limit?: number) {
    const query = limit ? `?limit=${limit}` : '';
    return this.request<{
      success: boolean;
      events: any[];
    }>(`/monitoring/events${query}`);
  }

  // Config endpoints
  async getConfig() {
    return this.request<{
      success: boolean;
      config: any;
    }>('/config');
  }

  // Optimized API endpoints (simplified paths)
  async getStats() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/stats');
  }

  async getUsers() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/users');
  }

  async getUsersStats() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/users/stats');
  }

  async getCalls() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/calls');
  }

  async getCDR(params?: { limit?: number; date?: string; caller?: string; callee?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.date) searchParams.append('date', params.date);
    if (params?.caller) searchParams.append('caller', params.caller);
    if (params?.callee) searchParams.append('callee', params.callee);

    const query = searchParams.toString();
    return this.request<{
      success: boolean;
      data: any;
    }>(`/cdr${query ? `?${query}` : ''}`);
  }

  async getHealth() {
    return this.request<{
      success: boolean;
      data: any;
    }>('/health');
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
export default apiClient;
