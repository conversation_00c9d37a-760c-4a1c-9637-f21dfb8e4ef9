// Configuration for OpenSIPS UI Frontend
const getConfig = () => {
  // Dynamic URL detection based on current location
  let dynamicBackendUrl = 'http://localhost:3001';

  if (typeof window !== 'undefined') {
    // Extract host from current URL and use port 3001 for backend
    const currentHost = window.location.hostname;
    const protocol = window.location.protocol;
    dynamicBackendUrl = `${protocol}//${currentHost}:3001`;
    console.log('🌐 Dynamic backend URL detected:', dynamicBackendUrl);
  }

  // Try multiple sources for environment variables with dynamic fallback
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL ||
                     (typeof window !== 'undefined' && (window as any).__NEXT_DATA__?.env?.NEXT_PUBLIC_BACKEND_URL) ||
                     dynamicBackendUrl;

  const apiUrl = process.env.NEXT_PUBLIC_API_URL ||
                 (typeof window !== 'undefined' && (window as any).__NEXT_DATA__?.env?.NEXT_PUBLIC_API_URL) ||
                 dynamicBackendUrl;

  const socketUrl = process.env.NEXT_PUBLIC_SOCKET_URL ||
                    (typeof window !== 'undefined' && (window as any).__NEXT_DATA__?.env?.NEXT_PUBLIC_SOCKET_URL) ||
                    dynamicBackendUrl;

  return {
    // API Configuration - prioritize BACKEND_URL
    apiUrl: backendUrl || apiUrl,
    socketUrl: backendUrl || socketUrl || apiUrl,

    // Environment
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',

    // Refresh interval for data updates (in milliseconds)
    refreshInterval: 5000,

    // WebSocket configuration
    websocket: {
      reconnectAttempts: 5,
      reconnectDelay: 1000,
      timeout: 10000
    },

    // Debug function
    debug: () => {
      const cfg = getConfig();
      console.log('🔧 Frontend Configuration:');
      console.log('  - API URL:', cfg.apiUrl);
      console.log('  - Socket URL:', cfg.socketUrl);
      console.log('  - Environment:', process.env.NODE_ENV);
      console.log('  - Raw NEXT_PUBLIC_BACKEND_URL:', process.env.NEXT_PUBLIC_BACKEND_URL);
      console.log('  - Raw NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
      console.log('  - Raw NEXT_PUBLIC_SOCKET_URL:', process.env.NEXT_PUBLIC_SOCKET_URL);
      console.log('  - window.location.href:', typeof window !== 'undefined' ? window.location.href : 'N/A (SSR)');
      console.log('  - window.__NEXT_DATA__:', typeof window !== 'undefined' ? (window as any).__NEXT_DATA__ : 'N/A (SSR)');
    }
  };
};

export const config = getConfig();

// Debug immediately when module loads
if (typeof window !== 'undefined') {
  console.log('🚀 Config module loaded in browser');
  config.debug();
}

export default config;
