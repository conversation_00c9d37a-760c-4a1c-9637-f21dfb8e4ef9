import { io, Socket } from 'socket.io-client';
import config from './config';

interface SocketOptions {
  url?: string;
  maxReconnectAttempts?: number;
  reconnectDelay?: number;
  timeout?: number;
}

class SocketService {
  private socket: Socket | null = null;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private timeout: number;
  private isConnecting = false;
  private connectionPromise: Promise<Socket> | null = null;
  private callbacks: {
    instantUserChange?: (data: any) => void;
    instantCallChange?: (data: any) => void;
  } = {};

  constructor(options: SocketOptions = {}) {
    this.url = options.url || config.socketUrl;
    this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
    this.reconnectDelay = options.reconnectDelay || 1000;
    this.timeout = options.timeout || 10000;

    console.log('🔧 SocketService initialized with URL:', this.url);
    config.debug(); // Debug configuration
  }

  // Connect to socket server with improved logic
  connect(): Promise<Socket> {
    // Return existing connection promise if already connecting
    if (this.isConnecting && this.connectionPromise) {
      console.log('⏳ Connection already in progress, returning existing promise');
      return this.connectionPromise;
    }

    // Return existing socket if already connected
    if (this.socket?.connected) {
      console.log('✅ Socket already connected');
      return Promise.resolve(this.socket);
    }

    this.isConnecting = true;
    this.connectionPromise = new Promise((resolve, reject) => {
      try {
        // Clean up existing socket
        this.cleanup();

        console.log('🔄 Connecting to Socket.IO server:', this.url);

        const socketOptions = {
          transports: ['websocket', 'polling'], // Prefer websocket first
          timeout: this.timeout,
          forceNew: true,
          autoConnect: true,
          reconnection: false, // Handle reconnection manually
          upgrade: true,
          rememberUpgrade: true,
          pingTimeout: 60000,
          pingInterval: 25000
        };

        console.log('🔧 Socket.IO options:', socketOptions);

        this.socket = io(this.url, socketOptions);

        // Set up event handlers
        this.setupEventHandlers(resolve, reject);

        console.log('📡 Socket.IO client created, waiting for connection...');

      } catch (error) {
        console.error('❌ Error creating socket:', error);
        this.isConnecting = false;
        this.connectionPromise = null;
        reject(error);
      }
    });

    return this.connectionPromise;
  }

  // Setup socket event handlers
  private setupEventHandlers(resolve: (socket: Socket) => void, reject: (error: any) => void) {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('✅ Connected to OpenSIPS UI Backend');
      console.log('🆔 Socket ID:', this.socket?.id);
      this.reconnectAttempts = 0;
      this.isConnecting = false;
      resolve(this.socket!);
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Socket connection error:', error);
      this.isConnecting = false;
      this.connectionPromise = null;

      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.scheduleReconnect();
      } else {
        console.error('❌ Max reconnection attempts reached');
        reject(error);
      }
    });

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 Disconnected from server:', reason);
      this.isConnecting = false;
      this.connectionPromise = null;

      // Auto-reconnect for certain disconnect reasons
      if (reason === 'io server disconnect' || reason === 'transport close') {
        this.scheduleReconnect();
      }
    });

    this.socket.on('welcome', (data) => {
      console.log('👋 Welcome message:', data.message);
    });

    this.socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Set connection timeout
    setTimeout(() => {
      if (!this.socket?.connected) {
        console.error('⏰ Socket connection timeout');
        reject(new Error('Connection timeout'));
      }
    }, 15000);
  }

  // Clean up socket connection
  private cleanup() {
    if (this.socket) {
      console.log('🧹 Cleaning up existing socket connection');
      this.socket.removeAllListeners();
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Schedule reconnection with exponential backoff
  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), 30000);

    console.log(`🔄 Scheduling reconnect attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (!this.socket?.connected) {
        console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
        this.connect().catch((error) => {
          console.error('❌ Reconnection failed:', error);
        });
      }
    }, delay);
  }

  // Subscribe to real-time statistics with improved error handling
  subscribeToStats(callback: (data: any) => void) {
    if (!this.socket?.connected) {
      console.warn('📊 Socket not connected, attempting to connect first...');
      this.connect().then(() => {
        this.subscribeToStats(callback);
      }).catch((error) => {
        console.error('❌ Failed to connect for stats subscription:', error);
      });
      return;
    }

    console.log('📊 socketService: Subscribing to stats...');
    this.socket.emit('subscribe-stats');
    this.socket.on('stats-update', callback);
  }

  // Unsubscribe from statistics
  unsubscribeFromStats() {
    if (!this.socket) {
      console.warn('📊 Socket not available for unsubscribe');
      return;
    }

    console.log('📊 socketService: Unsubscribing from stats...');
    this.socket.emit('unsubscribe-stats');
    this.socket.off('stats-update');
  }

  // Subscribe to user updates with improved error handling
  subscribeToUsers(callback: (data: any) => void) {
    if (!this.socket?.connected) {
      console.warn('👥 Socket not connected, attempting to connect first...');
      this.connect().then(() => {
        this.subscribeToUsers(callback);
      }).catch((error) => {
        console.error('❌ Failed to connect for users subscription:', error);
      });
      return;
    }

    console.log('👥 socketService: Subscribing to users...');
    this.socket.emit('subscribe-users');
    this.socket.on('users-update', callback);

    // Subscribe to MI-based events
    this.socket.on('user:registered', (data) => {
      console.log('👤 User registration event (MI):', data);
      // Trigger callback to refresh user list
      callback({ type: 'registration', data });
    });

    this.socket.on('user:unregistered', (data) => {
      console.log('👤 User unregistration event (MI):', data);
      // Trigger callback to refresh user list
      callback({ type: 'unregistration', data });
    });

    this.socket.on('location:stats', (data) => {
      console.log('📍 Location stats update (MI):', data);
      // Trigger callback to refresh user list
      callback({ type: 'location_stats', data });
    });

    // Listen for instant user changes (ultra-fast detection)
    this.socket.on('user:instant_change', (data) => {
      console.log('⚡ INSTANT USER CHANGE:', data);
      // Show immediate notification
      if (this.callbacks.instantUserChange) {
        this.callbacks.instantUserChange(data);
      }
      // Also trigger normal callback for refresh
      callback({ type: 'instant_user_change', data });
    });
  }

  // Subscribe to individual user status changes
  subscribeToUserStatus(callback: (data: any) => void) {
    if (!this.socket?.connected) {
      console.warn('👤 Socket not connected for user status');
      return;
    }

    console.log('👤 socketService: Subscribing to user status...');
    this.socket.on('user:status', callback);
  }

  // Subscribe to user registration events
  subscribeToUserRegistration(callback: (data: any) => void) {
    if (!this.socket?.connected) {
      console.warn('📝 Socket not connected for user registration');
      return;
    }

    console.log('📝 socketService: Subscribing to user registration...');
    this.socket.on('user:registration', callback);
  }

  // Subscribe to user expiration warnings
  subscribeToUserExpiring(callback: (data: any) => void) {
    if (!this.socket?.connected) {
      console.warn('⏰ Socket not connected for user expiring');
      return;
    }

    console.log('⏰ socketService: Subscribing to user expiring...');
    this.socket.on('user:expiring', callback);
  }

  // Unsubscribe from user updates
  unsubscribeFromUsers() {
    if (!this.socket) {
      console.warn('👥 Socket not available for unsubscribe');
      return;
    }

    console.log('👥 socketService: Unsubscribing from users...');
    this.socket.emit('unsubscribe-users');
    this.socket.off('users-update');
    this.socket.off('user:status');
    this.socket.off('user:registration');
    this.socket.off('user:expiring');
  }

  // Subscribe to call updates with improved error handling
  subscribeToCalls(callback: (data: any) => void) {
    if (!this.socket?.connected) {
      console.warn('📞 Socket not connected, attempting to connect first...');
      this.connect().then(() => {
        this.subscribeToCalls(callback);
      }).catch((error) => {
        console.error('❌ Failed to connect for calls subscription:', error);
      });
      return;
    }

    console.log('📞 socketService: Subscribing to calls...');
    this.socket.emit('subscribe-calls');
    this.socket.on('calls-update', callback);

    // Subscribe to MI-based call events
    this.socket.on('call:started', (data) => {
      console.log('📞 Call started event (MI):', data);
      // Trigger callback to refresh call list
      callback({ type: 'call_started', data });
    });

    this.socket.on('call:ended', (data) => {
      console.log('📞 Call ended event (MI):', data);
      // Trigger callback to refresh call list
      callback({ type: 'call_ended', data });
    });

    this.socket.on('dialog:stats', (data) => {
      console.log('📊 Dialog stats update (MI):', data);
      // Trigger callback to refresh call list
      callback({ type: 'dialog_stats', data });
    });

    // Listen for instant call changes (ultra-fast detection)
    this.socket.on('call:instant_change', (data) => {
      console.log('⚡ INSTANT CALL CHANGE:', data);
      // Show immediate notification
      if (this.callbacks.instantCallChange) {
        this.callbacks.instantCallChange(data);
      }
      // Also trigger normal callback for refresh
      callback({ type: 'instant_call_change', data });
    });
  }

  // Unsubscribe from call updates
  unsubscribeFromCalls() {
    if (!this.socket) {
      console.warn('📞 Socket not available for unsubscribe');
      return;
    }

    console.log('📞 socketService: Unsubscribing from calls...');
    this.socket.emit('unsubscribe-calls');
    this.socket.off('calls-update');
  }

  // Subscribe to events
  subscribeToEvents(callback: (data: any) => void) {
    if (!this.socket) {
      console.error('Socket not connected');
      return;
    }

    this.socket.on('events-update', callback);
  }

  // Unsubscribe from events
  unsubscribeFromEvents() {
    if (!this.socket) return;
    this.socket.off('events-update');
  }

  // Get socket instance
  getSocket(): Socket | null {
    return this.socket;
  }

  // Check if connected
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Generic event listener
  on(event: string, callback: (data: any) => void) {
    if (!this.socket) {
      console.error('Socket not connected');
      return;
    }
    this.socket.on(event, callback);
  }

  // Generic event emitter
  emit(event: string, data?: any) {
    if (!this.socket) {
      console.error('Socket not connected');
      return;
    }
    this.socket.emit(event, data);
  }

  // Remove event listener
  off(event: string, callback?: (data: any) => void) {
    if (!this.socket) return;
    if (callback) {
      this.socket.off(event, callback);
    } else {
      this.socket.off(event);
    }
  }

  // Disconnect socket with cleanup
  disconnect() {
    if (this.socket) {
      console.log('🔌 Disconnecting socket...');
      this.cleanup();
    }
    this.isConnecting = false;
    this.connectionPromise = null;
    this.reconnectAttempts = 0;
  }

  // Force reconnect
  forceReconnect(): Promise<Socket> {
    console.log('🔄 Force reconnecting...');
    this.disconnect();
    return this.connect();
  }

  // Get connection info
  getConnectionInfo() {
    return {
      connected: this.isConnected(),
      url: this.url,
      socketId: this.socket?.id,
      reconnectAttempts: this.reconnectAttempts,
      isConnecting: this.isConnecting
    };
  }
}

// Create singleton instance
export const socketService = new SocketService();
export default socketService;