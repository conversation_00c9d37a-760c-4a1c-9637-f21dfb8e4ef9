export interface CallRecord {
  id: number;
  callId: string;
  caller: string;
  callee: string;
  method: string;
  status: {
    code: string;
    reason: string;
  };
  time: string;
  duration: number;
  setupTime: number;
  created: string;
  isSuccessful: boolean;
}

export interface ActiveCall {
  id: number;
  callId: string;
  caller: {
    uri: string;
    tag: string;
    contact?: string;
  };
  callee: {
    uri: string;
    tag: string;
    contact?: string;
  };
  startTime: string;
  duration: number;
  state: number;
  timeout: number;
}

export interface MissedCall {
  id: number;
  callId: string;
  caller: string;
  callee: string;
  method: string;
  status: {
    code: string;
    reason: string;
  };
  time: string;
  setupTime: number;
  created: string;
}

export interface CallStats {
  period: string;
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  successRate: number;
  activeCalls: number;
  averageDuration: number;
  totalDuration: number;
}

export interface CallHistoryResponse {
  success: boolean;
  data: {
    calls: CallRecord[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface ActiveCallsResponse {
  success: boolean;
  data: {
    activeCalls: ActiveCall[];
    count: number;
  };
}

export interface CallStatsResponse {
  success: boolean;
  data: CallStats;
}

export interface MissedCallsResponse {
  success: boolean;
  data: {
    missedCalls: MissedCall[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

export interface CallFilters {
  from_date?: string;
  to_date?: string;
  caller?: string;
  callee?: string;
  status?: string;
}
