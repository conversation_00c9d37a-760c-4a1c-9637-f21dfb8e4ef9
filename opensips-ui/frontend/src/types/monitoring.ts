export interface SystemOverview {
  users: {
    total: number;
    online: number;
  };
  calls: {
    total24h: number;
    active: number;
    successful: number;
    failed: number;
    successRate: number;
    avgDuration: number;
  };
  system: {
    uptime: string;
    version: string;
    memory: string;
  };
}

export interface CallMetric {
  time: string;
  totalCalls: number;
  successfulCalls: number;
  failedCalls: number;
  avgDuration: number;
  successRate: string;
}

export interface TopCaller {
  caller: string;
  callCount: number;
  successfulCalls: number;
  avgDuration: number;
  successRate: string;
}

export interface ErrorDistribution {
  code: string;
  reason: string;
  count: number;
}

export interface Metrics {
  period: string;
  callMetrics: CallMetric[];
  topCallers: TopCaller[];
  errorDistribution: ErrorDistribution[];
  opensipsMetrics?: any;
}

export interface SystemHealth {
  database: 'healthy' | 'unhealthy' | 'unknown';
  opensips: 'healthy' | 'unhealthy' | 'unknown';
  overall: 'healthy' | 'degraded' | 'unknown';
  timestamp: string;
  databaseError?: string;
  opensipsError?: string;
}

export interface SystemOverviewResponse {
  success: boolean;
  data: SystemOverview;
}

export interface MetricsResponse {
  success: boolean;
  data: Metrics;
}

export interface SystemHealthResponse {
  success: boolean;
  data: SystemHealth;
}

export interface OpenSIPSStatsResponse {
  success: boolean;
  data: any;
}
