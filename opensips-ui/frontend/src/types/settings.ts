export interface SettingValue {
  value: string;
  description: string;
  type?: 'text' | 'number' | 'boolean' | 'select' | 'email' | 'password';
  options?: string[];
}

export interface SettingsCategory {
  [key: string]: SettingValue;
}

export interface SystemSettings {
  general: SettingsCategory;
  sip: SettingsCategory;
  security: SettingsCategory;
  notifications: SettingsCategory;
  monitoring: SettingsCategory;
}

export interface SettingsResponse {
  success: boolean;
  data: {
    settings: SystemSettings;
    lastUpdated: string;
  };
}

export interface SettingsCategoryResponse {
  success: boolean;
  data: {
    category: string;
    settings: SettingsCategory;
  };
}

export interface SettingsUpdateRequest {
  settings: Partial<SystemSettings>;
}

export interface SettingsUpdateResponse {
  success: boolean;
  message: string;
  data: {
    updatedAt: string;
  };
}

export interface SettingsResetRequest {
  category?: string;
}

export interface SettingsBackup {
  version: string;
  timestamp: string;
  settings: Array<{
    setting_key: string;
    setting_value: string;
    description: string;
    category: string;
  }>;
}
