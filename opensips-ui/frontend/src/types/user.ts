// User types for OpenSIPS UI

export interface User {
  username: string;
  domain: string;
  email_address?: string;
  password_hash?: string;
  contact?: string;
  expires?: number;
  status: 'online' | 'offline';
  user_agent?: string;
  last_seen?: string;
  last_seen_formatted?: string;
  received?: string;
  path?: string;
  socket?: string;
  methods?: string;
  cseq?: number;
  priority?: number;
  expires_in_seconds?: number;
  ip_address?: string;
}

export interface UserStats {
  total_users: number;
  online_users: number;
  offline_users: number;
  avg_expires_in?: number;
  unique_user_agents: number;
  unique_ip_addresses: number;
}

export interface UserAgent {
  user_agent: string;
  count: number;
  online_count: number;
}

export interface Domain {
  domain: string;
  total_users: number;
  online_users: number;
}

export interface UsersResponse {
  users: User[];
  total: number;
  online: number;
  offline: number;
}

export interface UsersStatsResponse {
  overview: UserStats;
  userAgents: UserAgent[];
  domains: Domain[];
  timestamp: string;
}

// UI specific types
export interface UserTableColumn {
  key: keyof User | 'actions';
  label: string;
  sortable?: boolean;
  width?: string;
  align?: 'left' | 'center' | 'right';
}

export interface UserFilter {
  status?: 'all' | 'online' | 'offline';
  domain?: string;
  search?: string;
  userAgent?: string;
}

export interface UserSort {
  field: keyof User;
  direction: 'asc' | 'desc';
}
