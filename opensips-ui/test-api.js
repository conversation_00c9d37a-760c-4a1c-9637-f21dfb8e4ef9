// Test script to debug API calls
const fetch = require('node-fetch');

async function testAPI() {
  try {
    console.log('🔍 Testing API endpoints...');
    
    // Test public users endpoint
    console.log('\n📍 Testing /api/public/users');
    const response = await fetch('http://localhost:3001/api/public/users');
    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Success:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ Failed:', response.statusText);
    }
    
    // Test CORS preflight
    console.log('\n📍 Testing CORS preflight');
    const corsResponse = await fetch('http://localhost:3001/api/public/users', {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    console.log('CORS Status:', corsResponse.status);
    console.log('CORS Headers:', Object.fromEntries(corsResponse.headers.entries()));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testAPI();
