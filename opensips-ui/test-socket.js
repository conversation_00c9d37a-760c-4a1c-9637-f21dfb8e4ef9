#!/usr/bin/env node

const { io } = require('socket.io-client');

console.log('🧪 Testing Socket.IO Connection...');

const socket = io('http://localhost:3001', {
  transports: ['websocket', 'polling'],
  timeout: 10000,
  forceNew: true,
  autoConnect: true,
  reconnection: true,
  reconnectionAttempts: 3,
  reconnectionDelay: 1000
});

socket.on('connect', () => {
  console.log('✅ Connected successfully!');
  console.log('🆔 Socket ID:', socket.id);
  console.log('🚀 Transport:', socket.io.engine.transport.name);
  
  // Test subscription
  console.log('📊 Testing stats subscription...');
  socket.emit('subscribe-stats');
  
  setTimeout(() => {
    console.log('🔌 Disconnecting...');
    socket.disconnect();
    process.exit(0);
  }, 5000);
});

socket.on('connect_error', (error) => {
  console.error('❌ Connection failed:', error.message);
  process.exit(1);
});

socket.on('disconnect', (reason) => {
  console.log('🔌 Disconnected:', reason);
});

socket.on('welcome', (data) => {
  console.log('👋 Welcome message:', data);
});

socket.on('stats-update', (data) => {
  console.log('📊 Stats update received:', data);
});

socket.on('error', (error) => {
  console.error('❌ Socket error:', error);
});

console.log('⏳ Attempting to connect...');
