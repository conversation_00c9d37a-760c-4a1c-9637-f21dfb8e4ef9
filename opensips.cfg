#
# OpenSIPS residential configuration script
# <AUTHOR> <EMAIL>
#
# This script was generated via "make menuconfig", from
# the "Residential" scenario.
# You can enable / disable more features / functionalities by
# re-generating the scenario with different options.#
#
# Please refer to the Core CookBook at:
# https://opensips.org/Resources/DocsCookbooks
# for a explanation of possible statements, functions and parameters.
#


# Global Parameters

/* uncomment the following lines to enable debugging */
debug_mode=yes

log_level=4
xlog_level=4
stderror_enabled=yes
syslog_enabled=yes
syslog_facility=LOG_LOCAL0

udp_workers=4

/* uncomment the next line to enable the auto temporary blacklisting of
   not available destinations (default disabled) */
#disable_dns_blacklist=no

/* uncomment the next line to enable IPv6 lookup after IPv4 dns
   lookup failures (default disabled) */
#dns_try_ipv6=yes

socket=udp:0.0.0.0:5060   # CUSTOMIZE ME

# Add aliases so OpenSIPS recognizes itself
alias=udp:localhost:5060
alias=udp:***********:5060
alias=udp:opensips.local:5060

# Set advertised address for Record-Route
advertised_address=***********

# Modules Section

#set module path
mpath="/usr/lib/x86_64-linux-gnu/opensips/modules/"

#### SIGNALING module
loadmodule "signaling.so"

#### StateLess module
loadmodule "sl.so"

#### Transaction Module
loadmodule "tm.so"
modparam("tm", "fr_timeout", 5)
modparam("tm", "fr_inv_timeout", 30)
modparam("tm", "restart_fr_on_each_reply", 0)
modparam("tm", "onreply_avp_mode", 1)
modparam("tm", "auto_100trying", 1)

#### Record Route Module
loadmodule "rr.so"
/* do not append from tag to the RR (no need for this script) */
modparam("rr", "append_fromtag", 0)

#### MAX ForWarD module
loadmodule "maxfwd.so"

#### SIP MSG OPerationS module
loadmodule "sipmsgops.so"

#### TEXT OPerationS module
loadmodule "textops.so"

#### HTTP Management Interface (available in softswitch-ce!)
loadmodule "httpd.so"
modparam("httpd", "port", 8080)

loadmodule "mi_http.so"
# mi_http uses default root="/mi" - no params needed

# Event modules commented out - not needed for basic functionality

#### DB_MYSQL module
loadmodule "db_mysql.so"

#### AUTH module
loadmodule "auth.so"

#### AUTH_DB module
loadmodule "auth_db.so"
modparam("auth_db", "db_url", "mysql://opensips:opensips_password@mysql/opensips")
modparam("auth_db", "calculate_ha1", yes)
modparam("auth_db", "password_column", "password")

# URI module not available in softswitch-ce image
# Will use simplified authentication without check_from/check_to

#### USeR LOCation module
loadmodule "usrloc.so"
modparam("usrloc", "nat_bflag", "NAT")
modparam("usrloc", "working_mode_preset", "single-instance-sql-write-back")
modparam("usrloc", "db_url", "mysql://opensips:opensips_password@mysql/opensips")
modparam("usrloc", "use_domain", 1)

#### REGISTRAR module
loadmodule "registrar.so"
modparam("registrar", "tcp_persistent_flag", "TCP_PERSISTENT")
# modparam("registrar", "received_avp", "$avp(received_nh)")
/* uncomment the next line not to allow more than 10 contacts per AOR */
#modparam("registrar", "max_contacts", 10)

#### DIALOG module (for call state tracking)
loadmodule "dialog.so"
modparam("dialog", "db_url", "mysql://opensips:opensips_password@mysql/opensips")
modparam("dialog", "db_mode", 1)
# Enable dialog events for real-time call tracking
# modparam("dialog", "dlg_flag", "DIALOG_FLAG")
# modparam("dialog", "timeout_avp", "$avp(dialog_timeout)")
modparam("dialog", "default_timeout", 3600)

#### ACCounting module
loadmodule "acc.so"
/* what special events should be accounted ? */
modparam("acc", "early_media", 0)
modparam("acc", "report_cancels", 0)
/* by default we do not adjust the direct of the sequential requests.
   if you enable this parameter, be sure to enable "append_fromtag"
   in "rr" module */
modparam("acc", "detect_direction", 0)
modparam("acc", "db_url", "mysql://opensips:opensips_password@mysql/opensips")

#### NAT helper module
loadmodule "nathelper.so"
modparam("nathelper", "natping_interval", 30)
modparam("nathelper", "ping_nated_only", 1)
modparam("nathelper", "sipping_bflag", "SIP_PING_FLAG")
modparam("nathelper", "sipping_from", "sip:pinger@localhost")

#### RTPProxy module
loadmodule "rtpproxy.so"
modparam("rtpproxy", "rtpproxy_sock", "udp:***********:22222")
# disable nortpproxy marker to force RTP proxy usage
modparam("rtpproxy", "nortpproxy_str", "")
modparam("rtpproxy", "rtpproxy_disable_tout", 60)
modparam("rtpproxy", "rtpproxy_retr", 5)

loadmodule "proto_udp.so"

# Routing Logic

# main request routing logic

route{
	if (!mf_process_maxfwd_header(10)) {
		send_reply(483,"Too Many Hops");
		exit;
	}

	if (has_totag()) {
		# sequential request within a dialog - match dialog first
		if (!match_dialog()) {
			# dialog not found - might be a new dialog
			if (is_method("INVITE")) {
				create_dialog();
			}
		}

		# handle hop-by-hop ACK (no routing required)
		if ( is_method("ACK") && t_check_trans() ) {
			xlog("ACK received for call: $ci\n");
			t_relay();
			exit;
		}

		# handle end-to-end ACK (no transaction)
		if ( is_method("ACK") ) {
			xlog("End-to-end ACK received for call: $ci\n");
			# ACK is routed based on Route headers
		}

		# handle BYE requests
		if ( is_method("BYE") ) {
			xlog("BYE received for call: $ci from $fu to $tu\n");
			# fix NAT for BYE requests
			force_rport();
			if (nat_uac_test("private-contact")) {
				fix_nated_contact();
			}
			# Dialog routing handled automatically
		}

		# sequential request within a dialog should
		# take the path determined by record-routing
		if ( !loose_route() ) {
			# we do record-routing for all our traffic, so we should not
			# receive any sequential requests without Route hdr.
			send_reply(404,"Not here");
			exit;
		}

		if (is_method("BYE")) {
			# do accounting even if the transaction fails
			do_accounting("log","failed");
		}

		# route it out to whatever destination was set by loose_route()
		# in $du (destination URI).
		route(relay);
		exit;
	}

	# CANCEL processing
	if (is_method("CANCEL")) {
		if (t_check_trans())
			t_relay();
		exit;
	}

	# absorb retransmissions, but do not create transaction
	t_check_trans();

	if ( !(is_method("REGISTER")  ) ) {

		if (is_myself("$fd")) {

		} else {
			# if caller is not local, then called number must be local

			if (!is_myself("$rd")) {
				send_reply(403,"Relay Forbidden");
				exit;
			}
		}

	}

	# preloaded route checking
	if (loose_route()) {
		xlog("L_ERR",
		"Attempt to route with preloaded Route's [$fu/$tu/$ru/$ci]");
		if (!is_method("ACK"))
			send_reply(403,"Preload Route denied");
		exit;
	}

	# record routing
	if (!is_method("REGISTER|MESSAGE"))
		record_route();

	# account only INVITEs
	if (is_method("INVITE")) {
		do_accounting("log");

		# authenticate INVITEs
		if (!proxy_authorize("", "subscriber")) {
			proxy_challenge("", "auth");
			exit;
		}

		# consume credentials
		consume_credentials();
	}

	# handle NAT detection for all requests
	if (nat_uac_test("private-contact,diff-ip-src-via,diff-port-src-via")) {
		setbflag("NAT");
		fix_nated_contact();
		force_rport();
	}

	if (!is_myself("$rd")) {
		append_hf("P-hint: outbound\r\n");
		route(relay);
	}

	# requests for my domain

	if (is_method("PUBLISH|SUBSCRIBE")) {
		send_reply(503, "Service Unavailable");
		exit;
	}

	if (is_method("REGISTER")) {
		# authenticate the REGISTER requests
		if (!www_authorize("", "subscriber")) {
			www_challenge("", "auth");
			exit;
		}

		# handle NAT detection
		if (nat_uac_test("private-contact,diff-ip-src-via,diff-port-src-via")) {
			if (is_method("REGISTER")) {
				fix_nated_register();
				setbflag("NAT");
			} else {
				fix_nated_contact();
				setbflag("NAT");
			}
			force_rport();
		}

		# store the registration and generate a SIP reply
		if (!save("location")) {
			xlog("failed to register AoR $tu\n");
		} else {
			xlog("L_NOTICE", "User registered: $rU@$rd");
		}

		exit;
	}

	if ($rU==NULL) {
		# request with no Username in RURI
		send_reply(484,"Address Incomplete");
		exit;
	}

	# do lookup with method filtering
	if (!lookup("location","method-filtering")) {
		t_reply(404, "Not Found");
		exit;
	}

	# when routing via usrloc, log the missed calls also
	do_accounting("log","missed");
	route(relay);
}

route[relay] {
	# for INVITEs enable some additional helper routes
	if (is_method("INVITE")) {
		xlog("L_NOTICE", "RELAY INVITE: $ru from $fu - Call-ID: $ci - CSeq: $cs");

		# fix NAT for INVITE before creating dialog
		if (nat_uac_test("private-contact")) {
			fix_nated_contact();
		}

		# create dialog for call tracking
		if (!has_totag()) {
			create_dialog();
			xlog("L_NOTICE", "Dialog created for Call-ID: $ci");

			xlog("L_NOTICE", "Call started: $ci");
		}

		# engage RTPProxy for media relay
		xlog("L_NOTICE", "INVITE Content-Type: $hdr(Content-Type), Content-Length: $hdr(Content-Length)");
		if (has_body("application/sdp")) {
			# log SDP before RTP proxy
			xlog("L_NOTICE", "SDP BEFORE RTP Proxy: $rb");

			# RTP proxy for all requests to handle NAT
			rtpproxy_offer("co");

			# log SDP after RTP proxy
			xlog("L_NOTICE", "SDP AFTER RTP Proxy: $rb");
		} else {
			xlog("L_NOTICE", "INVITE has no SDP body!");
		}

		t_on_branch("per_branch_ops");
		t_on_reply("handle_nat");
		t_on_failure("missed_call");
	}

	xlog("L_NOTICE", "About to t_relay for $rm - Call-ID: $ci");
	if (!t_relay()) {
		xlog("L_ERROR", "t_relay failed for $rm - Call-ID: $ci");
		send_reply(500,"Internal Error");
	} else {
		xlog("L_NOTICE", "t_relay successful for $rm - Call-ID: $ci");
	}
	exit;
}

# Event routes removed - using database polling for real-time updates

branch_route[per_branch_ops] {
	xlog("new branch at $ru\n");
}

onreply_route[handle_nat] {
	xlog("L_NOTICE", "ONREPLY: $rs $rr from $si:$sp - Call-ID: $ci - CSeq: $cs $rm");

	# handle 200 OK for INVITE (call answered)
	if (is_method("INVITE") && $rs =~ "2[0-9][0-9]") {
		xlog("L_NOTICE", "Call answered: $ci");
	}

	# handle NAT in replies - fix contact only once
	if (nat_uac_test("private-contact")) {
		fix_nated_contact();
	}

	# engage RTPProxy for media relay in replies
	xlog("L_NOTICE", "REPLY Content-Type: $hdr(Content-Type), Content-Length: $hdr(Content-Length)");
	if (has_body("application/sdp")) {
		# log SDP before RTP proxy
		xlog("L_NOTICE", "REPLY SDP BEFORE RTP Proxy: $rb");

		# RTP proxy for all replies to handle NAT
		rtpproxy_answer("co");

		# log SDP after RTP proxy
		xlog("L_NOTICE", "REPLY SDP AFTER RTP Proxy: $rb");
	} else {
		xlog("L_NOTICE", "REPLY has no SDP body!");
	}
}

failure_route[missed_call] {
	if (t_was_cancelled()) {
		exit;
	}

	# uncomment the following lines if you want to block client
	# redirect based on 3xx replies.
	#if (t_check_status("3[0-9][0-9]")) {
	#	t_reply(404,"Not found");
	#	exit;
	#}

}
