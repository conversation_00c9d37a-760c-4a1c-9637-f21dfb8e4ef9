-- OpenSIPS Database Schema - Complete Standard Tables
-- Based on OpenSIPS official database schema

-- Version table - tracks table versions for migration
CREATE TABLE IF NOT EXISTS `version` (
  `table_name` varchar(32) NOT NULL,
  `table_version` int(10) unsigned NOT NULL DEFAULT 0,
  UNIQUE KEY `t_name_idx` (`table_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert version information for all tables
INSERT INTO `version` (`table_name`, `table_version`) VALUES
('location', 1013),
('subscriber', 8),
('acc', 7),
('missed_calls', 4),
('uri', 1),
('dbaliases', 1),
('domain', 2),
('grp', 3),
('re_grp', 1),
('address', 3),
('speed_dial', 2),
('usr_preferences', 2),
('rtpproxy_sockets', 1),
('dialog', 11),
('dialplan', 5),
('dispatcher', 8),
('load_balancer', 3),
('dr_gateways', 6),
('dr_rules', 4),
('dr_carriers', 3),
('dr_groups', 2),
('dr_partitions', 1);

-- Core authentication and user tables

-- Subscriber table for authentication (version 8)
CREATE TABLE IF NOT EXISTS `subscriber` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(64) NOT NULL DEFAULT '',
  `domain` varchar(64) NOT NULL DEFAULT '',
  `password` varchar(25) NOT NULL DEFAULT '',
  `ha1` varchar(64) NOT NULL DEFAULT '',
  `ha1_sha256` varchar(64) NOT NULL DEFAULT '',
  `ha1_sha512t256` varchar(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_idx` (`username`,`domain`),
  KEY `username_idx` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- URI table for URI authentication
CREATE TABLE IF NOT EXISTS `uri` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(64) NOT NULL DEFAULT '',
  `domain` varchar(64) NOT NULL DEFAULT '',
  `uri_user` varchar(64) NOT NULL DEFAULT '',
  `last_modified` datetime NOT NULL DEFAULT '1900-01-01 00:00:01',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_idx` (`username`,`domain`,`uri_user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Location table for user registrations (USRLOC)
CREATE TABLE IF NOT EXISTS `location` (
  `contact_id` bigint(10) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(64) NOT NULL DEFAULT '',
  `domain` varchar(64) DEFAULT NULL,
  `contact` text NOT NULL,
  `received` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `expires` int(10) unsigned NOT NULL,
  `q` float(10,2) NOT NULL DEFAULT 1.00,
  `callid` varchar(255) NOT NULL DEFAULT 'Default-Call-ID',
  `cseq` int(11) NOT NULL DEFAULT 13,
  `last_modified` datetime NOT NULL DEFAULT '1900-01-01 00:00:01',
  `flags` int(11) NOT NULL DEFAULT 0,
  `cflags` varchar(255) DEFAULT NULL,
  `user_agent` varchar(255) NOT NULL DEFAULT '',
  `socket` varchar(64) DEFAULT NULL,
  `methods` int(11) DEFAULT NULL,
  `sip_instance` varchar(255) DEFAULT NULL,
  `kv_store` text DEFAULT NULL,
  `attr` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`contact_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Accounting tables

-- Accounting table for call records
CREATE TABLE IF NOT EXISTS `acc` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `method` varchar(16) NOT NULL DEFAULT '',
  `from_tag` varchar(64) NOT NULL DEFAULT '',
  `to_tag` varchar(64) NOT NULL DEFAULT '',
  `callid` varchar(64) NOT NULL DEFAULT '',
  `sip_code` varchar(3) NOT NULL DEFAULT '',
  `sip_reason` varchar(32) NOT NULL DEFAULT '',
  `time` datetime NOT NULL,
  `duration` int(11) unsigned NOT NULL DEFAULT 0,
  `ms_duration` int(11) unsigned NOT NULL DEFAULT 0,
  `setuptime` int(11) unsigned NOT NULL DEFAULT 0,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `callid_idx` (`callid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Missed calls table
CREATE TABLE IF NOT EXISTS `missed_calls` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `method` varchar(16) NOT NULL DEFAULT '',
  `from_tag` varchar(64) NOT NULL DEFAULT '',
  `to_tag` varchar(64) NOT NULL DEFAULT '',
  `callid` varchar(64) NOT NULL DEFAULT '',
  `sip_code` varchar(3) NOT NULL DEFAULT '',
  `sip_reason` varchar(32) NOT NULL DEFAULT '',
  `time` datetime NOT NULL,
  `setuptime` int(11) unsigned NOT NULL DEFAULT 0,
  `created` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `callid_idx` (`callid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Alias and domain tables

-- Database aliases table
CREATE TABLE IF NOT EXISTS `dbaliases` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `alias_username` varchar(64) NOT NULL DEFAULT '',
  `alias_domain` varchar(64) NOT NULL DEFAULT '',
  `username` varchar(64) NOT NULL DEFAULT '',
  `domain` varchar(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `alias_idx` (`alias_username`,`alias_domain`),
  KEY `target_idx` (`username`,`domain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Domain table for multi-domain support
CREATE TABLE IF NOT EXISTS `domain` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `domain` varchar(64) NOT NULL DEFAULT '',
  `attrs` varchar(255) DEFAULT NULL,
  `last_modified` datetime NOT NULL DEFAULT '1900-01-01 00:00:01',
  PRIMARY KEY (`id`),
  UNIQUE KEY `domain_idx` (`domain`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Group and permissions tables

-- Group table for user groups
CREATE TABLE IF NOT EXISTS `grp` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(64) NOT NULL DEFAULT '',
  `domain` varchar(64) NOT NULL DEFAULT '',
  `grp` varchar(64) NOT NULL DEFAULT '',
  `last_modified` datetime NOT NULL DEFAULT '1900-01-01 00:00:01',
  PRIMARY KEY (`id`),
  UNIQUE KEY `account_group_idx` (`username`,`domain`,`grp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Regular expression groups
CREATE TABLE IF NOT EXISTS `re_grp` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `reg_exp` varchar(128) NOT NULL DEFAULT '',
  `group_id` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  KEY `group_idx` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Address table for permissions
CREATE TABLE IF NOT EXISTS `address` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `grp` int(11) unsigned NOT NULL DEFAULT 1,
  `ip` varchar(50) NOT NULL,
  `mask` int(11) NOT NULL DEFAULT 32,
  `port` smallint(5) unsigned NOT NULL DEFAULT 0,
  `proto` varchar(4) NOT NULL DEFAULT 'any',
  `pattern` varchar(64) DEFAULT NULL,
  `context_info` varchar(32) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Speed dial table
CREATE TABLE IF NOT EXISTS `speed_dial` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(64) NOT NULL DEFAULT '',
  `domain` varchar(64) NOT NULL DEFAULT '',
  `sd_username` varchar(64) NOT NULL DEFAULT '',
  `sd_domain` varchar(64) NOT NULL DEFAULT '',
  `new_uri` varchar(255) NOT NULL DEFAULT '',
  `fname` varchar(64) NOT NULL DEFAULT '',
  `lname` varchar(64) NOT NULL DEFAULT '',
  `description` varchar(64) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`),
  UNIQUE KEY `speed_dial_idx` (`username`,`domain`,`sd_domain`,`sd_username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- User preferences table
CREATE TABLE IF NOT EXISTS `usr_preferences` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(64) NOT NULL DEFAULT '',
  `username` varchar(64) NOT NULL DEFAULT '0',
  `domain` varchar(64) NOT NULL DEFAULT '',
  `attribute` varchar(32) NOT NULL DEFAULT '',
  `type` int(11) NOT NULL DEFAULT 0,
  `value` varchar(128) NOT NULL DEFAULT '',
  `last_modified` datetime NOT NULL DEFAULT '1900-01-01 00:00:01',
  PRIMARY KEY (`id`),
  KEY `ua_idx` (`uuid`,`attribute`),
  KEY `uda_idx` (`username`,`domain`,`attribute`),
  KEY `value_idx` (`value`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Media relay tables

-- RTPProxy sockets table
CREATE TABLE IF NOT EXISTS `rtpproxy_sockets` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `rtpproxy_sock` text NOT NULL,
  `set_id` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Dialog table for dialog management
CREATE TABLE IF NOT EXISTS `dialog` (
  `dlg_id` bigint(10) unsigned NOT NULL,
  `callid` varchar(255) NOT NULL,
  `from_uri` varchar(255) NOT NULL,
  `from_tag` varchar(128) NOT NULL,
  `to_uri` varchar(255) NOT NULL,
  `to_tag` varchar(128) NOT NULL,
  `mangled_from_uri` varchar(255) DEFAULT NULL,
  `mangled_to_uri` varchar(255) DEFAULT NULL,
  `caller_cseq` varchar(11) NOT NULL,
  `callee_cseq` varchar(11) NOT NULL,
  `caller_ping_cseq` varchar(11) DEFAULT NULL,
  `callee_ping_cseq` varchar(11) DEFAULT NULL,
  `caller_route_set` text,
  `callee_route_set` text,
  `caller_contact` varchar(255) DEFAULT NULL,
  `callee_contact` varchar(255) DEFAULT NULL,
  `caller_sock` varchar(64) NOT NULL,
  `callee_sock` varchar(64) NOT NULL,
  `state` int(10) unsigned NOT NULL,
  `start_time` int(10) unsigned NOT NULL,
  `timeout` int(10) unsigned NOT NULL,
  `vars` blob,
  `profiles` text,
  `script_flags` int(10) unsigned NOT NULL DEFAULT 0,
  `module_flags` int(10) unsigned NOT NULL DEFAULT 0,
  `flags` int(10) unsigned NOT NULL DEFAULT 0,
  PRIMARY KEY (`dlg_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Dialplan table for number manipulation
CREATE TABLE IF NOT EXISTS `dialplan` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `dpid` int(11) NOT NULL,
  `pr` int(11) NOT NULL,
  `match_op` int(11) NOT NULL,
  `match_exp` varchar(64) NOT NULL,
  `match_flags` int(11) NOT NULL DEFAULT 0,
  `subst_exp` varchar(64) DEFAULT NULL,
  `repl_exp` varchar(32) DEFAULT NULL,
  `timerec` varchar(255) DEFAULT NULL,
  `disabled` int(11) NOT NULL DEFAULT 0,
  `attrs` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Dispatcher table for load balancing
CREATE TABLE IF NOT EXISTS `dispatcher` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `setid` int(11) NOT NULL DEFAULT 0,
  `destination` varchar(192) NOT NULL DEFAULT '',
  `socket` varchar(128) DEFAULT NULL,
  `state` int(11) NOT NULL DEFAULT 0,
  `weight` varchar(64) NOT NULL DEFAULT '1',
  `priority` int(11) NOT NULL DEFAULT 0,
  `attrs` varchar(255) DEFAULT NULL,
  `description` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dispatcher_setid_idx` (`setid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Load balancer table
CREATE TABLE IF NOT EXISTS `load_balancer` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` int(11) unsigned NOT NULL DEFAULT 0,
  `dst_uri` varchar(128) NOT NULL,
  `resources` varchar(255) NOT NULL,
  `probe_mode` int(11) unsigned NOT NULL DEFAULT 0,
  `description` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `dsturi_idx` (`dst_uri`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Dynamic routing tables

-- DR Gateways table
CREATE TABLE IF NOT EXISTS `dr_gateways` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `gwid` varchar(64) NOT NULL,
  `type` int(11) unsigned NOT NULL DEFAULT 0,
  `address` varchar(128) NOT NULL,
  `strip` int(11) unsigned NOT NULL DEFAULT 0,
  `pri_prefix` varchar(16) DEFAULT NULL,
  `attrs` varchar(255) DEFAULT NULL,
  `probe_mode` int(11) unsigned NOT NULL DEFAULT 0,
  `state` int(11) unsigned NOT NULL DEFAULT 0,
  `socket` varchar(128) DEFAULT NULL,
  `description` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `dr_gw_idx` (`gwid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- DR Rules table
CREATE TABLE IF NOT EXISTS `dr_rules` (
  `ruleid` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `groupid` varchar(255) NOT NULL,
  `prefix` varchar(64) NOT NULL,
  `timerec` varchar(255) DEFAULT NULL,
  `priority` int(11) NOT NULL DEFAULT 0,
  `routeid` varchar(255) DEFAULT NULL,
  `gwlist` varchar(255) DEFAULT NULL,
  `attrs` varchar(255) DEFAULT NULL,
  `description` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`ruleid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- Insert sample data for testing

-- Insert test users for authentication
INSERT IGNORE INTO `subscriber` (`username`, `domain`, `password`, `ha1`) VALUES
('1001', 'localhost', 'password123', MD5('1001:localhost:password123')),
('1002', 'localhost', 'password123', MD5('1002:localhost:password123')),
('1003', 'localhost', 'password123', MD5('1003:localhost:password123'));

-- Insert default domain
INSERT IGNORE INTO `domain` (`domain`) VALUES ('localhost');

-- Insert RTPProxy socket configuration
INSERT IGNORE INTO `rtpproxy_sockets` (`rtpproxy_sock`, `set_id`) VALUES
('udp:***********:22222', 0);
