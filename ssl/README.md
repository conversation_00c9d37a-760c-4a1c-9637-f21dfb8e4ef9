# SSL Certificates Directory

This directory contains SSL certificates for OpenSIPS TLS support.

## Files

- `cert.pem` - SSL certificate (auto-generated or custom)
- `key.pem` - Private key (auto-generated or custom)

## Auto-generation

SSL certificates are automatically generated during installation if they don't exist.

## Custom Certificates

To use custom certificates:

1. Replace `cert.pem` with your certificate
2. Replace `key.pem` with your private key
3. Restart OpenSIPS service

```bash
docker-compose restart opensips
```

## Security Note

- Private keys are excluded from Git repository
- Use proper certificates for production environments
- Consider using Let's Encrypt for public deployments
